"use client"

import React, { useEffect, useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { LucideIcon, GraduationCap } from "lucide-react"
import { cn } from "@/lib/utils"
import { usePathname } from "next/navigation"
import Image from "next/image"

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface NavBarProps {
  items: NavItem[]
  className?: string
}

export function NavBar({ items, className }: NavBarProps) {
  const [activeTab, setActiveTab] = useState(items[0]?.name || '')
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Update active tab based on current pathname
  useEffect(() => {
    const currentItem = items.find(item => item.url === pathname)
    if (currentItem) {
      setActiveTab(currentItem.name)
    }
  }, [pathname, items])

  return (
    <div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-purple-500/20",
        className,
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        {/* Logo (Left) */}
        <Link href="/" className="flex items-center gap-2 z-10">
          <div className="relative w-10 h-10 rounded-full overflow-hidden">
            <Image
              src="/images/logo/innohub_logo.png"
              alt="InnoHub Logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
        </Link>

        {/* Desktop Navigation (Center) */}
        <nav className="hidden md:flex items-center absolute left-1/2 transform -translate-x-1/2">
          <div className="flex items-center gap-1 bg-black/20 border border-purple-500/30 backdrop-blur-lg py-1 px-1 rounded-full shadow-lg">
            {items?.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.name

              return (
                <Link
                  key={item.name}
                  href={item.url}
                  onClick={() => setActiveTab(item.name)}
                  className={cn(
                    "relative cursor-pointer text-sm font-semibold px-4 py-2 rounded-full transition-all duration-300",
                    "text-white/80 hover:text-white",
                    isActive && "text-white",
                  )}
                >
                  <span className="relative z-10">{item.name}</span>
                  {isActive && (
                    <motion.div
                      layoutId="tubelight"
                      className="absolute inset-0 w-full bg-purple-600/20 rounded-full -z-10"
                      initial={false}
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 30,
                      }}
                    >
                      {/* Tubelight effect */}
                      <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-purple-500 rounded-t-full">
                        <div className="absolute w-12 h-6 bg-purple-500/30 rounded-full blur-md -top-2 -left-2" />
                        <div className="absolute w-8 h-6 bg-purple-500/40 rounded-full blur-md -top-1" />
                        <div className="absolute w-4 h-4 bg-purple-500/50 rounded-full blur-sm top-0 left-2" />
                      </div>
                    </motion.div>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Courses Button (Right) */}
        <div className="hidden md:flex items-center">
          <Link
            href="/courses"
            className={cn(
              "relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-all duration-300",
              "text-white/80 hover:text-white border border-purple-500/30 bg-purple-600/10 hover:bg-purple-600/20",
              pathname === '/courses' && "text-white bg-purple-600/30 border-purple-500",
            )}
          >
            <div className="flex items-center gap-2">
              <GraduationCap size={16} />
              <span>Courses</span>
            </div>
            {pathname === '/courses' && (
              <motion.div
                layoutId="courses-highlight"
                className="absolute inset-0 w-full bg-purple-600/20 rounded-full -z-10"
                initial={false}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                }}
              >
                {/* Tubelight effect for courses */}
                <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-purple-500 rounded-t-full">
                  <div className="absolute w-12 h-6 bg-purple-500/30 rounded-full blur-md -top-2 -left-2" />
                  <div className="absolute w-8 h-6 bg-purple-500/40 rounded-full blur-md -top-1" />
                  <div className="absolute w-4 h-4 bg-purple-500/50 rounded-full blur-sm top-0 left-2" />
                </div>
              </motion.div>
            )}
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button className="text-white p-2">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}
