'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  Users, 
  BookOpen, 
  FileText, 
  Settings, 
  Shield,
  TrendingUp,
  DollarSign,
  Eye,
  Award,
  Bell,
  Download
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { useSession, signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import AnalyticsDashboard from '@/components/admin/AnalyticsDashboard';
import BlogManagement from '@/components/admin/BlogManagement';
import CourseManagement from '@/components/admin/CourseManagement';

export default function AdminDashboard() {
  const { language } = useLanguage();
  const { data: session } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalCourses: 0,
    totalPosts: 0,
    totalRevenue: 0,
    activeUsers: 0,
    newSignups: 0
  });

  useEffect(() => {
    // Check for admin session in localStorage as fallback
    const adminSession = localStorage.getItem('innohub_admin');
    if ((session?.user && session.user.role === 'ADMIN') || adminSession === 'true') {
      loadStats();
    }
  }, [session]);

  // Check for admin session in localStorage as fallback
  const adminSession = typeof window !== 'undefined' ? localStorage.getItem('innohub_admin') : null;
  const isAdmin = (session?.user && session.user.role === 'ADMIN') || adminSession === 'true';

  // Show login redirect if not authenticated or not admin
  if (!isAdmin) {
    router.push('/auth/admin');
    return null;
  }

  const loadStats = async () => {
    try {
      // Load admin statistics
      // This would integrate with your analytics service
      setStats({
        totalUsers: 1250,
        totalCourses: 15,
        totalPosts: 45,
        totalRevenue: 2500000,
        activeUsers: 320,
        newSignups: 28
      });
    } catch (error) {
      console.error('Failed to load admin stats:', error);
    }
  };

  const handleLogout = () => {
    // Clear admin session
    localStorage.removeItem('innohub_admin');
    localStorage.removeItem('innohub_admin_user');
    // Redirect to home
    router.push('/');
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('mn-MN', {
      style: 'currency',
      currency: 'MNT',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    format = 'number' 
  }: {
    title: string;
    value: number;
    change?: number;
    icon: any;
    format?: 'number' | 'currency';
  }) => (
    <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-white">
              {format === 'currency' ? formatCurrency(value) : value.toLocaleString()}
            </p>
            {change !== undefined && (
              <div className={`flex items-center gap-1 text-sm ${
                change >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                <TrendingUp className="h-3 w-3" />
                <span>{Math.abs(change)}% from last month</span>
              </div>
            )}
          </div>
          <div className="p-3 bg-purple-500/20 rounded-lg">
            <Icon className="h-6 w-6 text-purple-400" />
          </div>
        </div>
      </CardContent>
    </Card>
  );



  return (
    <div className="min-h-screen bg-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-4xl font-bold text-white">
                {language === 'mn' ? 'Админ самбар' : 'Admin Dashboard'}
              </h1>
              <p className="text-gray-400 mt-2">
                {language === 'mn' ? 'Платформын удирдлага' : 'Platform management and analytics'}
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button
                variant="outline"
                className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
              >
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Button>
              <Button
                onClick={handleLogout}
                variant="outline"
                className="border-red-500/50 text-red-400 hover:bg-red-500/10 hover:border-red-500"
              >
                Logout
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="bg-black/40 border border-purple-500/20 p-1">
              <TabsTrigger 
                value="overview" 
                className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-white"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                {language === 'mn' ? 'Ерөнхий' : 'Overview'}
              </TabsTrigger>
              <TabsTrigger 
                value="analytics" 
                className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-white"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                {language === 'mn' ? 'Аналитик' : 'Analytics'}
              </TabsTrigger>
              <TabsTrigger 
                value="courses" 
                className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-white"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                {language === 'mn' ? 'Хичээлүүд' : 'Courses'}
              </TabsTrigger>
              <TabsTrigger 
                value="blog" 
                className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-white"
              >
                <FileText className="h-4 w-4 mr-2" />
                {language === 'mn' ? 'Блог' : 'Blog'}
              </TabsTrigger>
              <TabsTrigger 
                value="users" 
                className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-white"
              >
                <Users className="h-4 w-4 mr-2" />
                {language === 'mn' ? 'Хэрэглэгчид' : 'Users'}
              </TabsTrigger>
              <TabsTrigger 
                value="settings" 
                className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-white"
              >
                <Settings className="h-4 w-4 mr-2" />
                {language === 'mn' ? 'Тохиргоо' : 'Settings'}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                  title={language === 'mn' ? 'Нийт хэрэглэгч' : 'Total Users'}
                  value={stats.totalUsers}
                  change={12}
                  icon={Users}
                />
                <StatCard
                  title={language === 'mn' ? 'Нийт хичээл' : 'Total Courses'}
                  value={stats.totalCourses}
                  change={8}
                  icon={BookOpen}
                />
                <StatCard
                  title={language === 'mn' ? 'Нийт орлого' : 'Total Revenue'}
                  value={stats.totalRevenue}
                  change={25}
                  icon={DollarSign}
                  format="currency"
                />
                <StatCard
                  title={language === 'mn' ? 'Идэвхтэй хэрэглэгч' : 'Active Users'}
                  value={stats.activeUsers}
                  change={5}
                  icon={Eye}
                />
              </div>

              {/* Recent Activity */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-white">Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { action: 'New user registration', user: 'Batbayar M.', time: '2 minutes ago' },
                        { action: 'Course enrollment', user: 'Oyunaa S.', time: '15 minutes ago' },
                        { action: 'Payment completed', user: 'Munkh-Erdene B.', time: '1 hour ago' },
                        { action: 'Course completed', user: 'Tserendorj G.', time: '2 hours ago' }
                      ].map((activity, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                          <div>
                            <p className="text-white font-medium">{activity.action}</p>
                            <p className="text-gray-400 text-sm">{activity.user}</p>
                          </div>
                          <span className="text-gray-500 text-sm">{activity.time}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
                  <CardHeader>
                    <CardTitle className="text-white">Top Performing Courses</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { title: 'Entrepreneurship Fundamentals', enrollments: 245, revenue: 245000 },
                        { title: 'Digital Marketing Mastery', enrollments: 189, revenue: 283500 },
                        { title: 'Product Development', enrollments: 156, revenue: 310400 }
                      ].map((course, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                          <div>
                            <p className="text-white font-medium">{course.title}</p>
                            <p className="text-gray-400 text-sm">{course.enrollments} enrollments</p>
                          </div>
                          <div className="text-right">
                            <p className="text-purple-400 font-medium">{formatCurrency(course.revenue)}</p>
                            <Badge variant="outline" className="border-green-500/30 text-green-400">
                              #{index + 1}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="analytics">
              <AnalyticsDashboard />
            </TabsContent>

            <TabsContent value="courses">
              <CourseManagement />
            </TabsContent>

            <TabsContent value="blog">
              <BlogManagement />
            </TabsContent>

            <TabsContent value="users" className="space-y-6">
              <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-white">User Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">User Management</h3>
                    <p className="text-gray-400">User management features coming soon.</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-white">Platform Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Settings className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">Platform Settings</h3>
                    <p className="text-gray-400">Settings panel coming soon.</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
  );
}


