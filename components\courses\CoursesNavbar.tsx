'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LogOut, User, Settings } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { useSession, signOut } from 'next-auth/react';

const coursesNavLinks = [
  { href: '/courses/dashboard', label: 'Dashboard' },
  { href: '/courses/catalog', label: 'Courses' },
  { href: '/courses/profile', label: 'Profile' },
];

export default function CoursesNavbar() {
  const pathname = usePathname();
  const router = useRouter();
  const { data: session } = useSession();

  const handleLogout = async () => {
    await signOut({ redirect: false });
    router.push('/');
  };

  return (
    <nav className="bg-black/80 backdrop-blur-md border-b border-purple-500/30 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/courses/dashboard" className="flex items-center gap-3">
            <div className="relative w-10 h-10 rounded-full overflow-hidden">
              <Image
                src="/images/logo/innohub_logo.png"
                alt="InnoHub Logo"
                width={40}
                height={40}
                className="object-cover"
              />
            </div>
            <div>
              <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
              <div className="text-sm text-purple-400">Learning Platform</div>
            </div>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            {coursesNavLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`transition-colors font-medium ${
                  pathname === link.href
                    ? 'text-purple-400 border-b-2 border-purple-500'
                    : 'text-white/70 hover:text-white'
                }`}
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* User Menu */}
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="outline" size="sm" className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10">
                Back to Main
              </Button>
            </Link>

            {session?.user && (
              <div className="hidden md:flex items-center gap-3">
                <div className="text-right">
                  <div className="text-white font-medium">{session.user.name}</div>
                  <div className="text-purple-400 text-sm">
                    {session.user.role === 'ADMIN' ? 'Administrator' : 'Premium Learner'}
                  </div>
                </div>
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                  <User className="h-5 w-5 text-white" />
                </div>
              </div>
            )}

            {session?.user?.role === 'ADMIN' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/admin')}
                className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
              >
                <Settings className="h-4 w-4 mr-2" />
                Admin
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden pb-4">
          <div className="flex items-center justify-between">
            <div className="flex space-x-6">
              {coursesNavLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`font-medium ${
                    pathname === link.href ? 'text-purple-400' : 'text-white/70'
                  }`}
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
