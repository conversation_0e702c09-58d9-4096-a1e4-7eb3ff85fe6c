import { prisma } from '@/lib/prisma';

export interface BlogPost {
  id: string;
  title: string;
  titleMn?: string;
  slug: string;
  excerpt: string;
  excerptMn?: string;
  content: string;
  contentMn?: string;
  featuredImage?: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  category: string;
  tags: string[];
  isPublished: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  views: number;
  readTime: number; // in minutes
}

export interface BlogCategory {
  id: string;
  name: string;
  nameMn?: string;
  slug: string;
  description?: string;
  descriptionMn?: string;
  postCount: number;
}

export interface CreateBlogPostData {
  title: string;
  titleMn?: string;
  excerpt: string;
  excerptMn?: string;
  content: string;
  contentMn?: string;
  featuredImage?: string;
  category: string;
  tags: string[];
  isPublished: boolean;
  publishedAt?: Date;
}

export interface UpdateBlogPostData extends Partial<CreateBlogPostData> {
  id: string;
}

class BlogService {
  
  /**
   * Get all blog posts with pagination
   */
  async getAllPosts(options: {
    page?: number;
    limit?: number;
    category?: string;
    tag?: string;
    search?: string;
    published?: boolean;
    sortBy?: 'createdAt' | 'publishedAt' | 'views' | 'title';
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<{ posts: BlogPost[]; total: number; pages: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        category,
        tag,
        search,
        published,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const skip = (page - 1) * limit;
      const where: any = {};

      if (published !== undefined) {
        where.isPublished = published;
      }

      if (category) {
        where.category = category;
      }

      if (tag) {
        where.tags = { has: tag };
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { titleMn: { contains: search, mode: 'insensitive' } },
          { excerpt: { contains: search, mode: 'insensitive' } },
          { excerptMn: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } },
          { contentMn: { contains: search, mode: 'insensitive' } }
        ];
      }

      const [posts, total] = await Promise.all([
        prisma.blogPost.findMany({
          where,
          include: {
            author: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit
        }),
        prisma.blogPost.count({ where })
      ]);

      const pages = Math.ceil(total / limit);

      return {
        posts: posts as BlogPost[],
        total,
        pages
      };
    } catch (error) {
      console.error('Failed to get blog posts:', error);
      return { posts: [], total: 0, pages: 0 };
    }
  }

  /**
   * Get blog post by ID or slug
   */
  async getPost(identifier: string, incrementViews = false): Promise<BlogPost | null> {
    try {
      const isId = identifier.length === 24; // Assuming MongoDB ObjectId length
      const where = isId ? { id: identifier } : { slug: identifier };

      const post = await prisma.blogPost.findUnique({
        where,
        include: {
          author: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      if (post && incrementViews) {
        await prisma.blogPost.update({
          where: { id: post.id },
          data: { views: { increment: 1 } }
        });
      }

      return post as BlogPost | null;
    } catch (error) {
      console.error('Failed to get blog post:', error);
      return null;
    }
  }

  /**
   * Create new blog post
   */
  async createPost(authorId: string, data: CreateBlogPostData): Promise<BlogPost | null> {
    try {
      const slug = this.generateSlug(data.title);
      const readTime = this.calculateReadTime(data.content);

      const post = await prisma.blogPost.create({
        data: {
          ...data,
          slug,
          authorId,
          readTime,
          views: 0,
          publishedAt: data.isPublished ? (data.publishedAt || new Date()) : null
        },
        include: {
          author: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      return post as BlogPost;
    } catch (error) {
      console.error('Failed to create blog post:', error);
      return null;
    }
  }

  /**
   * Update blog post
   */
  async updatePost(data: UpdateBlogPostData): Promise<BlogPost | null> {
    try {
      const { id, ...updateData } = data;
      
      // Update slug if title changed
      if (updateData.title) {
        updateData.slug = this.generateSlug(updateData.title);
      }

      // Update read time if content changed
      if (updateData.content) {
        updateData.readTime = this.calculateReadTime(updateData.content);
      }

      // Set published date if publishing for the first time
      if (updateData.isPublished && !updateData.publishedAt) {
        const currentPost = await prisma.blogPost.findUnique({ where: { id } });
        if (currentPost && !currentPost.publishedAt) {
          updateData.publishedAt = new Date();
        }
      }

      const post = await prisma.blogPost.update({
        where: { id },
        data: updateData,
        include: {
          author: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      return post as BlogPost;
    } catch (error) {
      console.error('Failed to update blog post:', error);
      return null;
    }
  }

  /**
   * Delete blog post
   */
  async deletePost(id: string): Promise<boolean> {
    try {
      await prisma.blogPost.delete({ where: { id } });
      return true;
    } catch (error) {
      console.error('Failed to delete blog post:', error);
      return false;
    }
  }

  /**
   * Get all categories
   */
  async getCategories(): Promise<BlogCategory[]> {
    try {
      // Since we don't have a categories table yet, return hardcoded categories
      // In production, you'd create a categories table
      const categories = [
        { id: '1', name: 'Entrepreneurship', nameMn: 'Бизнес эрхлэлт', slug: 'entrepreneurship' },
        { id: '2', name: 'Technology', nameMn: 'Технологи', slug: 'technology' },
        { id: '3', name: 'Innovation', nameMn: 'Инноваци', slug: 'innovation' },
        { id: '4', name: 'Startup', nameMn: 'Стартап', slug: 'startup' },
        { id: '5', name: 'Business', nameMn: 'Бизнес', slug: 'business' },
        { id: '6', name: 'Education', nameMn: 'Боловсрол', slug: 'education' }
      ];

      // Get post counts for each category
      const categoriesWithCounts = await Promise.all(
        categories.map(async (category) => {
          const postCount = await prisma.blogPost.count({
            where: { category: category.name, isPublished: true }
          });
          return { ...category, postCount, description: '', descriptionMn: '' };
        })
      );

      return categoriesWithCounts;
    } catch (error) {
      console.error('Failed to get categories:', error);
      return [];
    }
  }

  /**
   * Get popular tags
   */
  async getPopularTags(limit = 20): Promise<{ tag: string; count: number }[]> {
    try {
      const posts = await prisma.blogPost.findMany({
        where: { isPublished: true },
        select: { tags: true }
      });

      const tagCounts = new Map<string, number>();
      
      posts.forEach(post => {
        post.tags.forEach(tag => {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
        });
      });

      return Array.from(tagCounts.entries())
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);
    } catch (error) {
      console.error('Failed to get popular tags:', error);
      return [];
    }
  }

  /**
   * Get related posts
   */
  async getRelatedPosts(postId: string, limit = 5): Promise<BlogPost[]> {
    try {
      const currentPost = await prisma.blogPost.findUnique({
        where: { id: postId },
        select: { category: true, tags: true }
      });

      if (!currentPost) return [];

      const relatedPosts = await prisma.blogPost.findMany({
        where: {
          AND: [
            { id: { not: postId } },
            { isPublished: true },
            {
              OR: [
                { category: currentPost.category },
                { tags: { hasSome: currentPost.tags } }
              ]
            }
          ]
        },
        include: {
          author: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { publishedAt: 'desc' },
        take: limit
      });

      return relatedPosts as BlogPost[];
    } catch (error) {
      console.error('Failed to get related posts:', error);
      return [];
    }
  }

  /**
   * Search posts
   */
  async searchPosts(query: string, options: {
    category?: string;
    limit?: number;
  } = {}): Promise<BlogPost[]> {
    try {
      const { category, limit = 10 } = options;
      
      const where: any = {
        isPublished: true,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { titleMn: { contains: query, mode: 'insensitive' } },
          { excerpt: { contains: query, mode: 'insensitive' } },
          { excerptMn: { contains: query, mode: 'insensitive' } },
          { content: { contains: query, mode: 'insensitive' } },
          { contentMn: { contains: query, mode: 'insensitive' } },
          { tags: { has: query } }
        ]
      };

      if (category) {
        where.category = category;
      }

      const posts = await prisma.blogPost.findMany({
        where,
        include: {
          author: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { publishedAt: 'desc' },
        take: limit
      });

      return posts as BlogPost[];
    } catch (error) {
      console.error('Failed to search posts:', error);
      return [];
    }
  }

  /**
   * Generate URL-friendly slug from title
   */
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
  }

  /**
   * Calculate estimated read time in minutes
   */
  private calculateReadTime(content: string): number {
    const wordsPerMinute = 200; // Average reading speed
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * Get blog analytics
   */
  async getBlogAnalytics(): Promise<{
    totalPosts: number;
    publishedPosts: number;
    draftPosts: number;
    totalViews: number;
    averageViews: number;
    topPosts: { title: string; views: number }[];
    categoriesStats: { category: string; count: number }[];
  }> {
    try {
      const [totalPosts, publishedPosts, draftPosts, viewsData, topPosts] = await Promise.all([
        prisma.blogPost.count(),
        prisma.blogPost.count({ where: { isPublished: true } }),
        prisma.blogPost.count({ where: { isPublished: false } }),
        prisma.blogPost.aggregate({
          _sum: { views: true },
          _avg: { views: true }
        }),
        prisma.blogPost.findMany({
          where: { isPublished: true },
          select: { title: true, views: true },
          orderBy: { views: 'desc' },
          take: 10
        })
      ]);

      const categories = await this.getCategories();
      const categoriesStats = categories.map(cat => ({
        category: cat.name,
        count: cat.postCount
      }));

      return {
        totalPosts,
        publishedPosts,
        draftPosts,
        totalViews: viewsData._sum.views || 0,
        averageViews: Math.round(viewsData._avg.views || 0),
        topPosts: topPosts.map(post => ({
          title: post.title,
          views: post.views
        })),
        categoriesStats
      };
    } catch (error) {
      console.error('Failed to get blog analytics:', error);
      return {
        totalPosts: 0,
        publishedPosts: 0,
        draftPosts: 0,
        totalViews: 0,
        averageViews: 0,
        topPosts: [],
        categoriesStats: []
      };
    }
  }
}

export const blogService = new BlogService();
export default blogService;
