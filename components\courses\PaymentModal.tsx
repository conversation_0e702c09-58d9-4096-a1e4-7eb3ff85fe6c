'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  Smartphone, 
  Building2, 
  Star, 
  Shield, 
  Clock,
  CheckCircle,
  AlertCircle,
  QrCode
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { useSession } from 'next-auth/react';
import paymentService, { MONGOLIAN_PAYMENT_METHODS, PaymentMethod } from '@/lib/services/paymentService';
import Image from 'next/image';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  course: {
    id: string;
    title: string;
    price: number;
    originalPrice?: number;
    instructor: string;
  };
  appliedCoupon?: {
    code: string;
    discountAmount: number;
  };
}

export default function PaymentModal({ isOpen, onClose, course, appliedCoupon }: PaymentModalProps) {
  const { t, language } = useLanguage();
  const { data: session } = useSession();
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [step, setStep] = useState<'select' | 'processing' | 'success' | 'error'>('select');

  const finalPrice = course.price - (appliedCoupon?.discountAmount || 0);
  const currency = 'MNT';

  const handlePayment = async () => {
    if (!selectedMethod || !session?.user?.id) return;

    setIsProcessing(true);
    setStep('processing');

    try {
      const result = await paymentService.processPayment({
        userId: session.user.id,
        courseId: course.id,
        amount: finalPrice,
        currency: 'MNT',
        paymentMethod: selectedMethod,
        couponCode: appliedCoupon?.code
      });

      setPaymentResult(result);

      if (result.success) {
        if (result.redirectUrl) {
          // Redirect to payment provider
          window.location.href = result.redirectUrl;
        } else if (result.qrCode) {
          // Show QR code for mobile payments
          setStep('success');
        }
      } else {
        setStep('error');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setStep('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'mobile':
        return <Smartphone className="h-5 w-5" />;
      case 'bank':
        return <Building2 className="h-5 w-5" />;
      case 'card':
        return <CreditCard className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('mn-MN', {
      style: 'currency',
      currency: 'MNT',
      minimumFractionDigits: 0
    }).format(price);
  };

  if (step === 'processing') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="bg-black/95 border-purple-500/20 text-white max-w-md">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-white mb-2">Processing Payment</h3>
            <p className="text-gray-400">Please wait while we process your payment...</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (step === 'success' && paymentResult?.qrCode) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="bg-black/95 border-purple-500/20 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Scan QR Code to Pay</DialogTitle>
          </DialogHeader>
          
          <div className="text-center space-y-6">
            <div className="bg-white p-4 rounded-lg mx-auto w-fit">
              <QrCode className="h-48 w-48 text-black" />
              {/* In production, render actual QR code */}
            </div>
            
            <div>
              <p className="text-gray-400 mb-2">Scan this QR code with your mobile app</p>
              <p className="text-2xl font-bold text-purple-400">{formatPrice(finalPrice)}</p>
            </div>

            <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
              <Clock className="h-4 w-4" />
              <span>Payment expires in 10 minutes</span>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (step === 'error') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="bg-black/95 border-red-500/20 text-white max-w-md">
          <div className="text-center py-8">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Payment Failed</h3>
            <p className="text-gray-400 mb-6">
              {paymentResult?.error || 'Something went wrong. Please try again.'}
            </p>
            <div className="flex gap-3">
              <Button
                onClick={() => setStep('select')}
                className="flex-1 bg-purple-500 hover:bg-purple-600 text-white"
              >
                Try Again
              </Button>
              <Button
                onClick={onClose}
                variant="outline"
                className="flex-1 border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-black/95 border-purple-500/20 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">
            {language === 'mn' ? 'Төлбөр төлөх' : 'Complete Payment'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Course Summary */}
          <Card className="border-purple-500/20 bg-purple-500/5">
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="font-semibold text-white">{course.title}</h3>
                  <p className="text-sm text-gray-400">by {course.instructor}</p>
                </div>
                <div className="text-right">
                  {course.originalPrice && course.originalPrice > course.price && (
                    <div className="text-sm text-gray-400 line-through">
                      {formatPrice(course.originalPrice)}
                    </div>
                  )}
                  <div className="text-lg font-bold text-white">
                    {formatPrice(course.price)}
                  </div>
                </div>
              </div>

              {appliedCoupon && (
                <div className="flex items-center justify-between p-2 bg-green-500/10 rounded border border-green-500/20">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm text-green-400">Coupon: {appliedCoupon.code}</span>
                  </div>
                  <span className="text-sm text-green-400">-{formatPrice(appliedCoupon.discountAmount)}</span>
                </div>
              )}

              <Separator className="my-3 bg-gray-600/30" />

              <div className="flex justify-between items-center">
                <span className="font-semibold text-white">
                  {language === 'mn' ? 'Нийт төлөх дүн:' : 'Total:'}
                </span>
                <span className="text-xl font-bold text-purple-400">
                  {formatPrice(finalPrice)}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">
              {language === 'mn' ? 'Төлбөрийн арга сонгох' : 'Choose Payment Method'}
            </h3>

            {/* Popular Methods */}
            <div className="space-y-3 mb-6">
              <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                <Star className="h-4 w-4" />
                {language === 'mn' ? 'Түгээмэл хэрэглэгддэг' : 'Popular Methods'}
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {MONGOLIAN_PAYMENT_METHODS.filter(method => method.popular).map((method) => (
                  <motion.div
                    key={method.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card 
                      className={`cursor-pointer transition-all ${
                        selectedMethod === method.id
                          ? 'border-purple-500 bg-purple-500/10'
                          : 'border-gray-600/30 bg-gray-700/20 hover:border-purple-500/50'
                      }`}
                      onClick={() => setSelectedMethod(method.id as PaymentMethod)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center">
                            {getMethodIcon(method.type)}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-white">
                              {language === 'mn' ? method.nameMn : method.nameEn}
                            </div>
                            <div className="text-sm text-gray-400">
                              {language === 'mn' ? method.descriptionMn : method.description}
                            </div>
                          </div>
                          {selectedMethod === method.id && (
                            <CheckCircle className="h-5 w-5 text-purple-400" />
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Other Methods */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-400">
                {language === 'mn' ? 'Бусад арга' : 'Other Methods'}
              </h4>
              
              <div className="space-y-2">
                {MONGOLIAN_PAYMENT_METHODS.filter(method => !method.popular).map((method) => (
                  <Card 
                    key={method.id}
                    className={`cursor-pointer transition-all ${
                      selectedMethod === method.id
                        ? 'border-purple-500 bg-purple-500/10'
                        : 'border-gray-600/30 bg-gray-700/20 hover:border-purple-500/50'
                    }`}
                    onClick={() => setSelectedMethod(method.id as PaymentMethod)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded bg-white/10 flex items-center justify-center">
                          {getMethodIcon(method.type)}
                        </div>
                        <div className="flex-1">
                          <span className="text-white">
                            {language === 'mn' ? method.nameMn : method.nameEn}
                          </span>
                        </div>
                        {selectedMethod === method.id && (
                          <CheckCircle className="h-4 w-4 text-purple-400" />
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="flex items-center gap-2 p-3 bg-blue-500/10 rounded border border-blue-500/20">
            <Shield className="h-5 w-5 text-blue-400" />
            <div className="text-sm text-blue-400">
              {language === 'mn' 
                ? 'Таны төлбөрийн мэдээлэл найдвартай хамгаалагдсан байна'
                : 'Your payment information is securely protected'
              }
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={onClose}
              variant="outline"
              className="flex-1 border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
            >
              {language === 'mn' ? 'Цуцлах' : 'Cancel'}
            </Button>
            <Button
              onClick={handlePayment}
              disabled={!selectedMethod || isProcessing}
              className="flex-1 bg-purple-500 hover:bg-purple-600 text-white"
            >
              {isProcessing 
                ? (language === 'mn' ? 'Боловсруулж байна...' : 'Processing...')
                : (language === 'mn' ? 'Төлбөр төлөх' : 'Pay Now')
              }
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
