'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Gift, 
  Tag, 
  Clock, 
  DollarSign, 
  TrendingUp,
  Calendar,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Copy,
  ExternalLink
} from 'lucide-react';
import couponService from '@/lib/services/couponService';
import { UserCouponStatus, Coupon, CouponRedemption } from '@/lib/types/coupon';
import { getCurrentUser } from '@/lib/auth';

export default function CouponDashboard() {
  const [couponStatus, setCouponStatus] = useState<UserCouponStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const currentUser = getCurrentUser();
    setUser(currentUser);
    
    if (currentUser) {
      loadCouponStatus(currentUser.id);
    }
  }, []);

  const loadCouponStatus = async (userId: string) => {
    try {
      const status = await couponService.getUserCouponStatus(userId);
      setCouponStatus(status);
    } catch (error) {
      console.error('Failed to load coupon status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const copyCouponCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDiscountText = (coupon: Coupon) => {
    if (coupon.discountType === 'free_access') {
      return 'FREE ACCESS';
    } else {
      return `${coupon.discountValue}% OFF`;
    }
  };

  const getCouponStatusColor = (coupon: Coupon) => {
    if (coupon.status !== 'active') return 'bg-gray-500/20 text-gray-400';
    if (coupon.expirationDate && new Date() > coupon.expirationDate) return 'bg-red-500/20 text-red-400';
    return 'bg-green-500/20 text-green-400';
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 w-48 bg-purple-500/20 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gray-700/20 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!couponStatus) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-12 w-12 text-gray-500 mx-auto mb-4" />
        <p className="text-gray-400">Failed to load coupon information</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Gift className="h-8 w-8 text-purple-500" />
        <h2 className="text-2xl font-bold text-white">My Coupons</h2>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
          <CardContent className="p-4 text-center">
            <Tag className="h-8 w-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">{couponStatus.availableCoupons.length}</div>
            <div className="text-sm text-gray-400">Available Coupons</div>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
          <CardContent className="p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">{couponStatus.appliedCoupons.length}</div>
            <div className="text-sm text-gray-400">Applied Coupons</div>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
          <CardContent className="p-4 text-center">
            <DollarSign className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">${couponStatus.totalSavings}</div>
            <div className="text-sm text-gray-400">Total Savings</div>
          </CardContent>
        </Card>
      </div>

      {/* Coupon Tabs */}
      <Tabs defaultValue="available" className="w-full">
        <TabsList className="bg-black/40 border border-purple-500/20">
          <TabsTrigger value="available" className="data-[state=active]:bg-purple-500/20">
            Available Coupons
          </TabsTrigger>
          <TabsTrigger value="applied" className="data-[state=active]:bg-purple-500/20">
            Applied Coupons
          </TabsTrigger>
          <TabsTrigger value="history" className="data-[state=active]:bg-purple-500/20">
            Coupon History
          </TabsTrigger>
        </TabsList>

        {/* Available Coupons */}
        <TabsContent value="available" className="space-y-4">
          {couponStatus.availableCoupons.length === 0 ? (
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8 text-center">
                <Tag className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">No Available Coupons</h3>
                <p className="text-gray-400">Check back later for new coupon opportunities!</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {couponStatus.availableCoupons.map((coupon, index) => (
                <motion.div
                  key={coupon.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md hover:border-purple-500/40 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                            <Sparkles className="h-5 w-5 text-purple-400" />
                          </div>
                          <div>
                            <div className="font-semibold text-white">{coupon.code}</div>
                            <Badge className={getCouponStatusColor(coupon)}>
                              {getDiscountText(coupon)}
                            </Badge>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyCouponCode(coupon.code)}
                          className="text-gray-400 hover:text-white"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>

                      <p className="text-gray-400 text-sm mb-3">{coupon.description}</p>

                      <div className="space-y-2 text-xs text-gray-500">
                        {coupon.expirationDate && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>Expires: {formatDate(coupon.expirationDate)}</span>
                          </div>
                        )}
                        {coupon.scope === 'course_specific' && (
                          <div className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            <span>Course-specific coupon</span>
                          </div>
                        )}
                        {coupon.usageType === 'single_use' && (
                          <div className="flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            <span>Single use only</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Applied Coupons */}
        <TabsContent value="applied" className="space-y-4">
          {couponStatus.appliedCoupons.length === 0 ? (
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8 text-center">
                <CheckCircle className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">No Applied Coupons</h3>
                <p className="text-gray-400">Apply a coupon to a course to see it here!</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {couponStatus.appliedCoupons.map((redemption, index) => (
                <motion.div
                  key={redemption.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="border-green-500/20 bg-green-500/5 backdrop-blur-md">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                            <CheckCircle className="h-5 w-5 text-green-400" />
                          </div>
                          <div>
                            <div className="font-semibold text-white">{redemption.couponCode}</div>
                            <div className="text-sm text-gray-400">
                              Applied on {formatDate(redemption.redeemedAt)}
                            </div>
                            <div className="text-sm text-green-400">
                              Saved ${redemption.discountAmount}
                            </div>
                          </div>
                        </div>
                        {redemption.courseId && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            View Course
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Coupon History */}
        <TabsContent value="history" className="space-y-4">
          {couponStatus.couponHistory.length === 0 ? (
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8 text-center">
                <Clock className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">No Coupon History</h3>
                <p className="text-gray-400">Your coupon usage history will appear here.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {couponStatus.couponHistory.map((redemption, index) => (
                <motion.div
                  key={redemption.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="border-purple-500/20 bg-black/40 backdrop-blur-md">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center">
                            <Tag className="h-4 w-4 text-purple-400" />
                          </div>
                          <div>
                            <div className="font-medium text-white text-sm">{redemption.couponCode}</div>
                            <div className="text-xs text-gray-400">
                              {formatDate(redemption.redeemedAt)}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-green-400">
                            -${redemption.discountAmount}
                          </div>
                          <div className="text-xs text-gray-400">
                            ${redemption.finalPrice} paid
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
