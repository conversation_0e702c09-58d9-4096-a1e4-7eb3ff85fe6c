# Appwrite Setup Guide for InnoHub

## 1. Create Appwrite Account

1. Go to [Appwrite Cloud](https://cloud.appwrite.io/) or set up self-hosted Appwrite
2. Create a new account or sign in
3. Create a new project called "InnoHub"

## 2. Configure Project

### Project Settings
1. Go to your project dashboard
2. Copy the Project ID
3. Add your domain (localhost:3000 for development) to the platforms

### Database Setup
1. Go to "Databases" in the sidebar
2. Create a new database called "innohub_db"
3. Copy the Database ID

### Storage Setup
1. Go to "Storage" in the sidebar
2. Create a new bucket called "innohub_storage"
3. Copy the Storage ID

## 3. Create Collections

Create the following collections in your database:

### Users Collection
- Collection ID: `users`
- Attributes:
  - `email` (string, required, unique)
  - `name` (string, required)
  - `role` (enum: STUDENT, INSTRUCTOR, ADMIN, default: STUDENT)
  - `isOnboarded` (boolean, default: false)
  - `avatar` (string, optional)
  - `bio` (string, optional)
  - `skills` (string array, optional)
  - `interests` (string array, optional)
  - `createdAt` (datetime, required)
  - `updatedAt` (datetime, required)

### Courses Collection
- Collection ID: `courses`
- Attributes:
  - `title` (string, required)
  - `description` (string, required)
  - `longDescription` (string, optional)
  - `thumbnail` (string, optional)
  - `duration` (string, required)
  - `level` (enum: BEGINNER, INTERMEDIATE, ADVANCED)
  - `category` (string, required)
  - `instructor` (string, required)
  - `instructorId` (string, optional)
  - `price` (integer, required)
  - `isPublished` (boolean, default: false)
  - `tags` (string array, optional)
  - `enrollmentCount` (integer, default: 0)
  - `rating` (float, default: 0)
  - `totalLessons` (integer, default: 0)
  - `createdAt` (datetime, required)
  - `updatedAt` (datetime, required)

### Lessons Collection
- Collection ID: `lessons`
- Attributes:
  - `courseId` (string, required)
  - `title` (string, required)
  - `description` (string, optional)
  - `videoId` (string, optional)
  - `videoUrl` (string, optional)
  - `duration` (string, required)
  - `order` (integer, required)
  - `isPublished` (boolean, default: false)
  - `content` (string, optional)
  - `resources` (string array, optional)
  - `createdAt` (datetime, required)
  - `updatedAt` (datetime, required)

### Enrollments Collection
- Collection ID: `enrollments`
- Attributes:
  - `userId` (string, required)
  - `courseId` (string, required)
  - `enrolledAt` (datetime, required)
  - `completedAt` (datetime, optional)
  - `progress` (integer, default: 0)
  - `status` (enum: ACTIVE, COMPLETED, CANCELLED, default: ACTIVE)

### Blog Posts Collection
- Collection ID: `blog_posts`
- Attributes:
  - `title` (string, required)
  - `content` (string, required)
  - `excerpt` (string, required)
  - `slug` (string, required, unique)
  - `category` (string, required)
  - `tags` (string array, optional)
  - `isPublished` (boolean, default: false)
  - `publishedAt` (datetime, optional)
  - `authorId` (string, required)
  - `authorName` (string, required)
  - `authorEmail` (string, required)
  - `views` (integer, default: 0)
  - `readTime` (integer, required)
  - `featuredImage` (string, optional)
  - `metaDescription` (string, optional)
  - `seoKeywords` (string array, optional)
  - `createdAt` (datetime, required)
  - `updatedAt` (datetime, required)

### Progress Collection
- Collection ID: `progress`
- Attributes:
  - `userId` (string, required)
  - `courseId` (string, required)
  - `lessonId` (string, required)
  - `completed` (boolean, default: false)
  - `completedAt` (datetime, optional)
  - `watchTime` (integer, default: 0)

### Coupons Collection
- Collection ID: `coupons`
- Attributes:
  - `code` (string, required, unique)
  - `name` (string, required)
  - `description` (string, optional)
  - `discountType` (enum: percentage, free_access)
  - `discountValue` (integer, required)
  - `scope` (enum: course_specific, platform_wide)
  - `applicableCourseIds` (string array, optional)
  - `usageType` (enum: single_use, limited_uses, unlimited)
  - `maxUses` (integer, optional)
  - `currentUses` (integer, default: 0)
  - `startDate` (datetime, required)
  - `expirationDate` (datetime, optional)
  - `status` (enum: active, inactive, expired, default: active)
  - `createdBy` (string, required)
  - `createdAt` (datetime, required)
  - `lastModified` (datetime, required)

## 4. Set Permissions

For each collection, set appropriate permissions:

### Read Permissions
- Users: Any authenticated user can read
- Courses: Any user can read published courses
- Lessons: Any enrolled user can read
- Blog Posts: Any user can read published posts

### Write Permissions
- Users: Users can update their own data, admins can manage all
- Courses: Instructors and admins can create/update
- Lessons: Course instructors and admins can manage
- Blog Posts: Authors and admins can manage

## 5. Environment Variables

Copy `.env.example` to `.env.local` and fill in your Appwrite credentials:

```bash
cp .env.example .env.local
```

Update the values with your actual Appwrite project details.

## 6. Authentication Setup

1. Go to "Auth" in Appwrite dashboard
2. Enable Email/Password authentication
3. Configure OAuth providers if needed (Google, etc.)
4. Set up email templates for verification, password reset, etc.

## 7. Storage Configuration

1. Configure file upload limits
2. Set allowed file types for course thumbnails and blog images
3. Configure image transformations if needed

## 8. Testing

After setup, test the integration:

1. Create a test user
2. Create a test course
3. Create a test blog post
4. Verify all CRUD operations work

## Benefits of Using Appwrite

✅ **Real-time Database**: Automatic real-time updates
✅ **Built-in Authentication**: User management out of the box
✅ **File Storage**: Handle course thumbnails and blog images
✅ **Security**: Built-in permissions and security rules
✅ **Scalability**: Handles growth automatically
✅ **No Database Setup**: No need to manage PostgreSQL
✅ **Rich Queries**: Advanced filtering and searching
✅ **Cloud or Self-hosted**: Flexible deployment options
