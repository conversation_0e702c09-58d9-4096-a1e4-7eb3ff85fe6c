'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InvestorCompanyCardProps {
  name: string;
  logo: string;
  description: string;
  investmentFocus: string[];
  website: string;
  featured?: boolean;
  index?: number;
}

export function InvestorCompanyCard({
  name,
  logo,
  description,
  investmentFocus,
  website,
  featured = false,
  index = 0,
}: InvestorCompanyCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // Mouse position values for 3D effect
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Smooth spring physics for mouse movement
  const springConfig = { damping: 25, stiffness: 300 };
  const smoothMouseX = useSpring(mouseX, springConfig);
  const smoothMouseY = useSpring(mouseY, springConfig);

  // Transform mouse position into rotation values
  const rotateX = useTransform(smoothMouseY, [-100, 100], [5, -5]);
  const rotateY = useTransform(smoothMouseX, [-100, 100], [-5, 5]);



  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    mouseX.set(e.clientX - centerX);
    mouseY.set(e.clientY - centerY);
  };

  return (
    <motion.div
      ref={cardRef}
      className={cn(
        'group relative overflow-hidden backdrop-blur-xl border h-full',
        'bg-gradient-to-br from-black/80 via-purple-950/20 to-black/80',
        'border-white/20 hover:border-purple-400/50',
        'transform-gpu shadow-2xl shadow-purple-500/10 will-change-transform',
        'transition-all duration-700 ease-out',
        'rounded-3xl',
        featured ? 'md:col-span-2' : ''
      )}
      initial={{ opacity: 0, y: 40, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 1,
        delay: index * 0.1,
        ease: [0.22, 1, 0.36, 1]
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        mouseX.set(0);
        mouseY.set(0);
      }}
      onMouseMove={handleMouseMove}
      style={{
        rotateX,
        rotateY,
        transformStyle: "preserve-3d",
        perspective: 1200,
      }}
      whileHover={{
        scale: 1.03,
        y: -12,
        rotateY: 2,
        transition: { duration: 0.4, ease: "easeOut" }
      }}
    >
      {/* Revolutionary Background System */}
      <motion.div
        className="absolute inset-0 opacity-0"
        animate={{
          opacity: isHovered ? 1 : 0,
        }}
        transition={{ duration: 0.6 }}
      >
        {/* Multi-layer gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/30 via-blue-500/20 to-indigo-500/30" />
        <div className="absolute inset-0 bg-gradient-to-tl from-pink-500/20 via-transparent to-cyan-500/20" />
      </motion.div>

      {/* Advanced Glow System */}
      <motion.div
        className="absolute inset-0 rounded-3xl"
        animate={{
          boxShadow: isHovered
            ? [
                "0 0 60px rgba(139, 92, 246, 0.6)",
                "0 0 120px rgba(139, 92, 246, 0.3)",
                "inset 0 0 30px rgba(139, 92, 246, 0.2)",
                "0 25px 50px rgba(0, 0, 0, 0.5)"
              ].join(", ")
            : "0 0 0px rgba(139, 92, 246, 0)",
        }}
        transition={{ duration: 0.6 }}
      />

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden rounded-3xl">
        {Array.from({ length: 6 }).map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute w-1 h-1 bg-purple-400/40 rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 3) * 20}%`,
            }}
            animate={isHovered ? {
              opacity: [0, 1, 0],
              scale: [0, 1.5, 0],
              y: [-10, -30, -50],
            } : {}}
            transition={{
              duration: 2,
              repeat: isHovered ? Infinity : 0,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>

      {/* Sophisticated overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/[0.03] opacity-60" />

      {/* Micro grid pattern */}
      <div className="absolute inset-0 bg-grid-white/[0.015] bg-[length:30px_30px] opacity-40" />

      <div className="p-10 flex flex-col h-full relative z-10">
        {/* Revolutionary Logo Design */}
        <div className="relative mb-8 flex justify-center">
          <motion.div
            className="relative w-32 h-32 rounded-2xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 p-4 shadow-2xl"
            whileHover={{
              scale: 1.1,
              rotate: 5,
              boxShadow: "0 20px 40px rgba(139, 92, 246, 0.3)"
            }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            {/* Logo glow effect */}
            <motion.div
              className="absolute inset-0 rounded-2xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 opacity-0"
              animate={{
                opacity: isHovered ? 1 : 0,
              }}
              transition={{ duration: 0.4 }}
            />

            <motion.div
              initial={{ scale: 0.9, opacity: 0.8 }}
              whileHover={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className="w-full h-full relative z-10"
            >
              <Image
                src={logo}
                alt={name}
                fill
                className="object-contain p-2"
                sizes="(max-width: 768px) 128px, 128px"
              />
            </motion.div>
          </motion.div>
        </div>

        {/* Revolutionary Content Design */}
        <div className="flex-grow text-center">
          <motion.h3
            className="text-3xl font-bold text-white mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-blue-400 group-hover:bg-clip-text transition-all duration-500"
            whileHover={{ scale: 1.05 }}
          >
            {name}
          </motion.h3>

          <motion.p
            className="text-white/70 mb-8 text-lg leading-relaxed"
            initial={{ opacity: 0.7 }}
            whileHover={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {description}
          </motion.p>

          <div className="mb-8">
            <motion.h4
              className="text-sm uppercase text-purple-300 mb-4 font-semibold tracking-widest"
              initial={{ opacity: 0.6 }}
              whileHover={{ opacity: 1 }}
            >
              Investment Focus
            </motion.h4>
            <div className="flex flex-wrap gap-3 justify-center">
              {investmentFocus.map((focus) => (
                <motion.span
                  key={focus}
                  className="relative px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/30 to-blue-500/30 text-white border border-purple-400/30 backdrop-blur-sm text-sm font-medium overflow-hidden"
                  whileHover={{
                    scale: 1.1,
                    backgroundColor: "rgba(139, 92, 246, 0.5)",
                    borderColor: "rgba(139, 92, 246, 0.8)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Tag glow effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-blue-400/20 opacity-0"
                    whileHover={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                  <span className="relative z-10">{focus}</span>
                </motion.span>
              ))}
            </div>
          </div>
        </div>

        {/* Revolutionary Footer Design */}
        <motion.div
          className="mt-8 pt-8 border-t border-gradient-to-r from-transparent via-purple-500/30 to-transparent"
          initial={{ opacity: 0.8 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Link
              href={website}
              target="_blank"
              rel="noopener noreferrer"
              className="relative inline-flex items-center justify-center gap-3 w-full py-4 px-6 rounded-2xl bg-gradient-to-r from-purple-600/20 to-blue-600/20 hover:from-purple-500/30 hover:to-blue-500/30 border border-purple-400/30 hover:border-purple-400/50 text-white font-semibold text-lg transition-all duration-500 group/link overflow-hidden backdrop-blur-sm"
            >
              {/* Animated background */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"
                initial={{ x: "-100%" }}
                whileHover={{ x: "100%" }}
                transition={{ duration: 0.8 }}
              />

              <span className="relative z-10 flex items-center gap-3">
                <span>Visit Website</span>
                <ExternalLink className="h-5 w-5 group-hover/link:translate-x-1 group-hover/link:scale-110 transition-all duration-300" />
              </span>

              {/* Glow effect */}
              <motion.div
                className="absolute inset-0 rounded-2xl opacity-0 group-hover/link:opacity-100 transition-opacity duration-500"
                style={{
                  boxShadow: "0 0 30px rgba(139, 92, 246, 0.5), inset 0 0 20px rgba(139, 92, 246, 0.2)"
                }}
              />
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  );
}
