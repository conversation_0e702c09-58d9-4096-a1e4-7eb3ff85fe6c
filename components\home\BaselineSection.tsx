'use client';

import React, { useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useLanguage } from '@/lib/context/language-context';
import { TextGradientOpacityOnScroll } from '@/components/ui/text-generate-effect';
import { useMobile, useMobilePerformance } from '@/hooks/use-mobile';
import { MobileOptimizedContainer } from '@/components/ui/mobile-optimized-container';

export default function BaselineSection() {
  const { t, language } = useLanguage();
  const [isMounted, setIsMounted] = useState(false);
  const { isMobile } = useMobile();
  const { shouldReduceMotion } = useMobilePerformance();

  // Parallax scroll effect - reduced on mobile for performance
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, isMobile ? 100 : 300]);

  // Set mounted state for client-side animations
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <div className={`${isMobile ? 'min-h-[80vh]' : 'min-h-[100vh]'} flex items-center justify-center bg-gradient-to-b from-black via-black/98 to-black/90 relative overflow-hidden border-t border-purple-900/20 border-b border-b-purple-900/10 shadow-t-lg`}>
      {/* Subtle gradient background */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(147,51,234,0.15),transparent_70%)] pointer-events-none"></div>

      {/* Animated glow effects - reduced on mobile */}
      {!shouldReduceMotion && (
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className={`absolute ${isMobile ? 'w-32 h-32' : 'w-64 h-64'} rounded-full bg-purple-500/10 blur-3xl`}
            style={{ top: '10%', left: '5%' }}
            animate={{
              opacity: [0.3, 0.5, 0.3],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
          />
          <motion.div
            className={`absolute ${isMobile ? 'w-48 h-48' : 'w-96 h-96'} rounded-full bg-purple-700/10 blur-3xl`}
            style={{ bottom: '5%', right: '10%' }}
            animate={{
              opacity: [0.2, 0.4, 0.2],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: 'reverse',
            }}
          />
        </div>
      )}

      {/* Floating particles */}
      <div className="absolute inset-0 z-0 opacity-30">
        {Array.from({ length: 40 }).map((_, i) => {
          // Generate a unique ID for each particle that doesn't rely on index
          const uniqueId = `particle-${i}-${Math.random().toString(36).substring(2, 9)}`;
          const particleWidth = Math.random() * 2 + 1;
          const particleHeight = Math.random() * 2 + 1;
          const leftPosition = Math.random() * 100;
          const topPosition = Math.random() * 100;
          const animationDuration = Math.random() * 5 + 5;
          const animationDelay = Math.random() * 5;

          return (
            <motion.div
              key={uniqueId}
              className="absolute rounded-full bg-white"
              style={{
                width: `${particleWidth}px`,
                height: `${particleHeight}px`,
                left: `${leftPosition}%`,
                top: `${topPosition}%`,
              }}
              animate={{
                opacity: [0, 0.8, 0],
                y: [0, -20, 0],
              }}
              transition={{
                duration: animationDuration,
                repeat: Infinity,
                delay: animationDelay,
              }}
            />
          );
        })}
      </div>

      {/* Content container with optimized parallax effect */}
      <MobileOptimizedContainer
        enableMotion
        motionProps={{
          style: { y: shouldReduceMotion ? 0 : y },
          className: "relative z-10 py-12 sm:py-16 md:py-0"
        }}
        spacing="normal"
        maxWidth="2xl"
      >
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isMounted ? 1 : 0 }}
          transition={{ duration: 1 }}
          className="text-center"
        >
          <div className="inline-block mb-8 sm:mb-10 w-full">
            <motion.div
              className="h-0.5 w-12 bg-gradient-to-r from-transparent via-purple-500 to-transparent mx-auto"
              initial={{ width: 0 }}
              animate={{ width: isMounted ? 150 : 0 }}
              transition={{ duration: 1.5, delay: 0.5 }}
            />
          </div>

          <div className="mb-8 sm:mb-12 max-w-5xl mx-auto">
            {isMounted && (
              <TextGradientOpacityOnScroll
                key={`text-gradient-${language}`} // Force re-render when language changes
                words={t('baseline.text')}
                className={`${
                  isMobile
                    ? 'text-lg sm:text-xl'
                    : 'text-xl sm:text-2xl md:text-3xl lg:text-4xl'
                } text-white/90 font-light leading-relaxed tracking-wide px-4 sm:px-6`}
              />
            )}
          </div>

          <motion.div
            className="h-0.5 w-12 bg-gradient-to-r from-transparent via-purple-500 to-transparent mx-auto mt-8 sm:mt-10"
            initial={{ width: 0 }}
            animate={{ width: isMounted ? 150 : 0 }}
            transition={{ duration: 1.5, delay: 3 }}
          />

          {/* Scroll indicator */}
          <motion.div
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: [0, 1, 0], y: [0, 10, 0] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: 'loop',
              delay: 4
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-purple-400"
            >
              <path d="M12 5v14"></path>
              <path d="m19 12-7 7-7-7"></path>
            </svg>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
}
