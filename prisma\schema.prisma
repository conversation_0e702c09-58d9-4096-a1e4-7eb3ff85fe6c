// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and profiles
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  image     String?
  role      Role     @default(STUDENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Authentication
  accounts Account[]
  sessions Session[]

  // Course relationships
  enrollments     Enrollment[]
  progress        LessonProgress[]
  couponRedemptions CouponRedemption[]
  assessmentResults AssessmentResult[]
  certificates    Certificate[]

  // Blog relationships
  blogPosts       BlogPost[]

  @@map("users")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Course system models
model Course {
  id          String   @id @default(cuid())
  title       String
  description String
  longDescription String? @db.Text
  thumbnail   String?
  duration    String
  level       Level
  category    String
  instructor  String
  price       Float    @default(0)
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Course content
  modules     Module[]
  enrollments Enrollment[]
  coupons     CourseCoupon[]
  assessments Assessment[]
  certificates Certificate[]

  @@map("courses")
}

model Module {
  id       String @id @default(cuid())
  title    String
  order    Int
  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  lessons Lesson[]

  @@map("modules")
}

model Lesson {
  id          String  @id @default(cuid())
  title       String
  description String?
  videoId     String?
  duration    String?
  order       Int
  moduleId    String
  module      Module  @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  progress LessonProgress[]

  @@map("lessons")
}

model Enrollment {
  id           String           @id @default(cuid())
  userId       String
  courseId     String
  enrolledAt   DateTime         @default(now())
  completedAt  DateTime?
  status       EnrollmentStatus @default(ACTIVE)

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("enrollments")
}

model LessonProgress {
  id           String   @id @default(cuid())
  userId       String
  lessonId     String
  isCompleted  Boolean  @default(false)
  watchTime    Int      @default(0) // in seconds
  lastPosition Int      @default(0) // video position in seconds
  completedAt  DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  lesson Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@unique([userId, lessonId])
  @@map("lesson_progress")
}

// Coupon system models
model Coupon {
  id              String        @id @default(cuid())
  code            String        @unique
  name            String
  description     String?
  discountType    DiscountType
  discountValue   Float
  scope           CouponScope
  usageType       UsageType
  maxUses         Int?
  currentUses     Int           @default(0)
  startDate       DateTime
  expirationDate  DateTime?
  status          CouponStatus  @default(ACTIVE)
  isWelcomeCoupon Boolean       @default(false)
  isReferralCoupon Boolean      @default(false)
  minimumPrice    Float?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relationships
  courseCoupons CourseCoupon[]
  redemptions   CouponRedemption[]

  @@map("coupons")
}

model CourseCoupon {
  id       String @id @default(cuid())
  couponId String
  courseId String

  coupon Coupon @relation(fields: [couponId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([couponId, courseId])
  @@map("course_coupons")
}

model CouponRedemption {
  id            String   @id @default(cuid())
  userId        String
  couponId      String
  courseId      String?
  originalPrice Float
  discountAmount Float
  finalPrice    Float
  redeemedAt    DateTime @default(now())
  isActive      Boolean  @default(true)

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  coupon Coupon @relation(fields: [couponId], references: [id], onDelete: Cascade)

  @@map("coupon_redemptions")
}

// Assessment system models
model Assessment {
  id          String @id @default(cuid())
  title       String
  description String?
  courseId    String
  passingScore Int    @default(70)
  timeLimit   Int?   // in minutes
  isRequired  Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  course    Course             @relation(fields: [courseId], references: [id], onDelete: Cascade)
  questions AssessmentQuestion[]
  results   AssessmentResult[]

  @@map("assessments")
}

model AssessmentQuestion {
  id           String       @id @default(cuid())
  assessmentId String
  question     String
  type         QuestionType
  options      Json?        // For multiple choice questions
  correctAnswer String
  points       Int          @default(1)
  order        Int

  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  @@map("assessment_questions")
}

model AssessmentResult {
  id           String   @id @default(cuid())
  userId       String
  assessmentId String
  score        Int
  totalPoints  Int
  passed       Boolean
  answers      Json     // Store user answers
  completedAt  DateTime @default(now())

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  @@map("assessment_results")
}

model Certificate {
  id          String   @id @default(cuid())
  userId      String
  courseId    String
  certificateNumber String @unique
  issuedAt    DateTime @default(now())
  verificationHash String @unique

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@map("certificates")
}

// Enums
enum Role {
  STUDENT
  INSTRUCTOR
  ADMIN
}

// Blog Post model for content management
model BlogPost {
  id          String   @id @default(cuid())
  title       String
  titleMn     String?
  slug        String   @unique
  excerpt     String
  excerptMn   String?
  content     String   @db.Text
  contentMn   String?  @db.Text
  featuredImage String?
  category    String
  tags        String[]
  isPublished Boolean  @default(false)
  publishedAt DateTime?
  views       Int      @default(0)
  readTime    Int      @default(5) // in minutes
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Author relationship
  authorId    String
  author      User     @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("blog_posts")
}

enum Level {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  SUSPENDED
}

enum DiscountType {
  PERCENTAGE
  FREE_ACCESS
}

enum CouponScope {
  COURSE_SPECIFIC
  PLATFORM_WIDE
}

enum UsageType {
  SINGLE_USE
  LIMITED_USES
  UNLIMITED
}

enum CouponStatus {
  ACTIVE
  INACTIVE
  EXPIRED
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SHORT_ANSWER
  ESSAY
}
