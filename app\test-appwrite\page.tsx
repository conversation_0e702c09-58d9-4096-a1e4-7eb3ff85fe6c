'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  Database, 
  User, 
  BookOpen, 
  FileText,
  Loader2,
  TestTube
} from 'lucide-react';

// Import Appwrite services
import { getAppwriteConfig, getAccount, getDatabases } from '@/lib/appwrite/config';
import { appwriteUserService } from '@/lib/appwrite/users';
import { appwriteCourseService } from '@/lib/appwrite/courses';
import { appwriteBlogService } from '@/lib/appwrite/blogs';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: any;
}

export default function TestAppwritePage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [testUser, setTestUser] = useState({
    email: '<EMAIL>',
    password: 'testpassword123',
    name: 'Test User'
  });

  const updateTestResult = (name: string, status: 'success' | 'error', message: string, details?: any) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.details = details;
        return [...prev];
      } else {
        return [...prev, { name, status, message, details }];
      }
    });
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    // Test 1: Configuration Check
    updateTestResult('Configuration', 'pending', 'Checking configuration...');
    try {
      const appwriteConfig = getAppwriteConfig();
      const config = {
        url: appwriteConfig.url,
        projectId: appwriteConfig.projectId,
        databaseId: appwriteConfig.databaseId,
        hasCollections: !!(appwriteConfig.usersCollectionId && appwriteConfig.coursesCollectionId)
      };
      
      if (!config.projectId) {
        throw new Error('Project ID not configured');
      }
      
      updateTestResult('Configuration', 'success', 'Configuration loaded successfully', config);
    } catch (error: any) {
      updateTestResult('Configuration', 'error', error.message);
    }

    // Test 2: Database Connection
    updateTestResult('Database Connection', 'pending', 'Testing database connection...');
    try {
      // Test connection by trying to get current user (simpler test)
      const account = getAccount();
      const currentUser = await account.get();
      const config = getAppwriteConfig();
      updateTestResult('Database Connection', 'success', 'Connected to Appwrite successfully', {
        endpoint: config.url,
        projectId: config.projectId,
        userLoggedIn: !!currentUser
      });
    } catch (error: any) {
      // If user is not logged in, that's still a successful connection
      if (error.message.includes('unauthorized') || error.message.includes('401')) {
        const config = getAppwriteConfig();
        updateTestResult('Database Connection', 'success', 'Connected to Appwrite successfully (no user logged in)', {
          endpoint: config.url,
          projectId: config.projectId,
          userLoggedIn: false
        });
      } else {
        updateTestResult('Database Connection', 'error', `Connection failed: ${error.message}`);
      }
    }

    // Test 3: Collections Check
    updateTestResult('Collections Check', 'pending', 'Checking collections...');
    try {
      const appwriteConfig = getAppwriteConfig();
      if (!appwriteConfig.databaseId) {
        throw new Error('Database ID not configured');
      }

      // Test by trying to access a specific collection
      const testCollectionIds = [
        appwriteConfig.usersCollectionId,
        appwriteConfig.coursesCollectionId,
        appwriteConfig.blogPostsCollectionId
      ];

      const collectionResults = [];
      const databases = getDatabases();
      for (const collectionId of testCollectionIds) {
        if (collectionId) {
          try {
            await databases.listDocuments(appwriteConfig.databaseId, collectionId, []);
            collectionResults.push(`${collectionId}: ✅ Accessible`);
          } catch (error: any) {
            if (error.message.includes('unauthorized') || error.message.includes('401')) {
              collectionResults.push(`${collectionId}: ✅ Exists (permission restricted)`);
            } else {
              collectionResults.push(`${collectionId}: ❌ ${error.message}`);
            }
          }
        }
      }

      updateTestResult('Collections Check', 'success', `Checked ${collectionResults.length} collections`, {
        collections: collectionResults
      });
    } catch (error: any) {
      updateTestResult('Collections Check', 'error', `Failed to check collections: ${error.message}`);
    }

    // Test 4: User Service Test
    updateTestResult('User Service', 'pending', 'Testing user operations...');
    try {
      // Try to get current user (will fail if not logged in, which is expected)
      const currentUser = await appwriteUserService.getCurrentUser();
      updateTestResult('User Service', 'success', 'User service is working', {
        currentUser: currentUser ? 'Logged in' : 'Not logged in'
      });
    } catch (error: any) {
      // This is expected if no user is logged in
      updateTestResult('User Service', 'success', 'User service is accessible (no user logged in)');
    }

    // Test 5: Course Service Test
    updateTestResult('Course Service', 'pending', 'Testing course operations...');
    try {
      const courses = await appwriteCourseService.getAllCourses();
      updateTestResult('Course Service', 'success', `Course service working. Found ${courses.length} courses`, {
        courseCount: courses.length
      });
    } catch (error: any) {
      updateTestResult('Course Service', 'error', `Course service failed: ${error.message}`);
    }

    // Test 6: Blog Service Test
    updateTestResult('Blog Service', 'pending', 'Testing blog operations...');
    try {
      const blogs = await appwriteBlogService.getAllPosts({ limit: 5 });
      updateTestResult('Blog Service', 'success', `Blog service working. Found ${blogs.posts.length} posts`, {
        postCount: blogs.posts.length,
        totalPosts: blogs.total
      });
    } catch (error: any) {
      updateTestResult('Blog Service', 'error', `Blog service failed: ${error.message}`);
    }

    setIsRunning(false);
  };

  const createTestUser = async () => {
    try {
      const user = await appwriteUserService.createUser({
        email: testUser.email,
        password: testUser.password,
        name: testUser.name,
        role: 'STUDENT'
      });
      
      updateTestResult('Create Test User', 'success', 'Test user created successfully', user);
    } catch (error: any) {
      updateTestResult('Create Test User', 'error', `Failed to create user: ${error.message}`);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <Loader2 className="h-5 w-5 text-yellow-500 animate-spin" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-500/20 bg-green-500/5';
      case 'error':
        return 'border-red-500/20 bg-red-500/5';
      case 'pending':
        return 'border-yellow-500/20 bg-yellow-500/5';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 p-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <TestTube className="h-8 w-8 text-purple-400" />
            <h1 className="text-3xl font-bold text-white">Appwrite Connection Test</h1>
          </div>
          <p className="text-gray-400">Testing InnoHub's Appwrite integration</p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Test Controls */}
          <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Database className="h-5 w-5" />
                Test Controls
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={runTests}
                disabled={isRunning}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              >
                {isRunning ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Running Tests...
                  </>
                ) : (
                  'Run All Tests'
                )}
              </Button>

              <div className="border-t border-gray-700 pt-4">
                <h3 className="text-white font-medium mb-3">Create Test User</h3>
                <div className="space-y-3">
                  <div>
                    <Label className="text-gray-300">Email</Label>
                    <Input
                      value={testUser.email}
                      onChange={(e) => setTestUser({...testUser, email: e.target.value})}
                      className="bg-gray-800 border-gray-600 text-white"
                    />
                  </div>
                  <div>
                    <Label className="text-gray-300">Name</Label>
                    <Input
                      value={testUser.name}
                      onChange={(e) => setTestUser({...testUser, name: e.target.value})}
                      className="bg-gray-800 border-gray-600 text-white"
                    />
                  </div>
                  <Button
                    onClick={createTestUser}
                    variant="outline"
                    className="w-full border-purple-500/50 text-purple-400 hover:bg-purple-500/10"
                  >
                    Create Test User
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Configuration Display */}
          <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-white">Current Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-gray-400">Endpoint</Label>
                <p className="text-white text-sm font-mono break-all">{getAppwriteConfig().url}</p>
              </div>
              <div>
                <Label className="text-gray-400">Project ID</Label>
                <p className="text-white text-sm font-mono">{getAppwriteConfig().projectId || 'Not configured'}</p>
              </div>
              <div>
                <Label className="text-gray-400">Database ID</Label>
                <p className="text-white text-sm font-mono">{getAppwriteConfig().databaseId || 'Not configured'}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8"
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-white">Test Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {testResults.map((result, index) => (
                    <motion.div
                      key={result.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(result.status)}
                          <h3 className="text-white font-medium">{result.name}</h3>
                        </div>
                        <Badge variant="outline" className={
                          result.status === 'success' ? 'border-green-500 text-green-400' :
                          result.status === 'error' ? 'border-red-500 text-red-400' :
                          'border-yellow-500 text-yellow-400'
                        }>
                          {result.status}
                        </Badge>
                      </div>
                      <p className="text-gray-300 text-sm mb-2">{result.message}</p>
                      {result.details && (
                        <pre className="text-xs text-gray-400 bg-gray-800/50 p-2 rounded overflow-x-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      )}
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}
