import { prisma } from '@/lib/prisma';
import { Assessment, AssessmentQuestion, AssessmentResult } from '@prisma/client';

export interface AssessmentWithQuestions extends Assessment {
  questions: AssessmentQuestion[];
}

export interface AssessmentResultWithDetails extends AssessmentResult {
  assessment: Assessment;
}

export interface QuizAnswer {
  questionId: string;
  answer: string;
  isCorrect?: boolean;
}

export interface QuizSubmission {
  assessmentId: string;
  userId: string;
  answers: QuizAnswer[];
  timeSpent: number; // in seconds
}

class AssessmentService {
  
  /**
   * Get all assessments for a course
   */
  async getCourseAssessments(courseId: string): Promise<AssessmentWithQuestions[]> {
    try {
      return await prisma.assessment.findMany({
        where: { courseId },
        include: {
          questions: {
            orderBy: { order: 'asc' }
          }
        },
        orderBy: { createdAt: 'asc' }
      });
    } catch (error) {
      console.error('Failed to fetch course assessments:', error);
      return [];
    }
  }

  /**
   * Get assessment by ID with questions
   */
  async getAssessmentById(assessmentId: string): Promise<AssessmentWithQuestions | null> {
    try {
      return await prisma.assessment.findUnique({
        where: { id: assessmentId },
        include: {
          questions: {
            orderBy: { order: 'asc' }
          }
        }
      });
    } catch (error) {
      console.error('Failed to fetch assessment:', error);
      return null;
    }
  }

  /**
   * Create a new assessment
   */
  async createAssessment(data: {
    title: string;
    description?: string;
    courseId: string;
    passingScore?: number;
    timeLimit?: number;
    isRequired?: boolean;
    questions: {
      question: string;
      type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY';
      options?: any;
      correctAnswer: string;
      points?: number;
    }[];
  }): Promise<AssessmentWithQuestions | null> {
    try {
      const { questions, ...assessmentData } = data;
      
      const assessment = await prisma.assessment.create({
        data: {
          ...assessmentData,
          passingScore: assessmentData.passingScore || 70,
          isRequired: assessmentData.isRequired || false,
        }
      });

      // Create questions
      const createdQuestions = await Promise.all(
        questions.map((question, index) =>
          prisma.assessmentQuestion.create({
            data: {
              ...question,
              assessmentId: assessment.id,
              order: index + 1,
              points: question.points || 1,
            }
          })
        )
      );

      return {
        ...assessment,
        questions: createdQuestions
      };
    } catch (error) {
      console.error('Failed to create assessment:', error);
      return null;
    }
  }

  /**
   * Submit quiz answers and calculate score
   */
  async submitQuiz(submission: QuizSubmission): Promise<AssessmentResult | null> {
    try {
      const assessment = await this.getAssessmentById(submission.assessmentId);
      if (!assessment) {
        throw new Error('Assessment not found');
      }

      // Calculate score
      let totalPoints = 0;
      let earnedPoints = 0;
      const gradedAnswers: QuizAnswer[] = [];

      for (const question of assessment.questions) {
        totalPoints += question.points;
        
        const userAnswer = submission.answers.find(a => a.questionId === question.id);
        if (userAnswer) {
          const isCorrect = this.checkAnswer(question, userAnswer.answer);
          gradedAnswers.push({
            ...userAnswer,
            isCorrect
          });
          
          if (isCorrect) {
            earnedPoints += question.points;
          }
        }
      }

      const score = Math.round((earnedPoints / totalPoints) * 100);
      const passed = score >= assessment.passingScore;

      // Save result
      const result = await prisma.assessmentResult.create({
        data: {
          userId: submission.userId,
          assessmentId: submission.assessmentId,
          score,
          totalPoints,
          passed,
          answers: gradedAnswers,
        }
      });

      return result;
    } catch (error) {
      console.error('Failed to submit quiz:', error);
      return null;
    }
  }

  /**
   * Get user's assessment results
   */
  async getUserResults(userId: string, courseId?: string): Promise<AssessmentResultWithDetails[]> {
    try {
      const where: any = { userId };
      
      if (courseId) {
        where.assessment = { courseId };
      }

      return await prisma.assessmentResult.findMany({
        where,
        include: {
          assessment: true
        },
        orderBy: { completedAt: 'desc' }
      });
    } catch (error) {
      console.error('Failed to fetch user results:', error);
      return [];
    }
  }

  /**
   * Get assessment analytics
   */
  async getAssessmentAnalytics(assessmentId: string): Promise<{
    totalAttempts: number;
    averageScore: number;
    passRate: number;
    questionAnalytics: {
      questionId: string;
      question: string;
      correctRate: number;
      totalAnswers: number;
    }[];
  }> {
    try {
      const results = await prisma.assessmentResult.findMany({
        where: { assessmentId },
        include: { assessment: { include: { questions: true } } }
      });

      const totalAttempts = results.length;
      const averageScore = totalAttempts > 0 
        ? results.reduce((sum, r) => sum + r.score, 0) / totalAttempts 
        : 0;
      const passRate = totalAttempts > 0 
        ? (results.filter(r => r.passed).length / totalAttempts) * 100 
        : 0;

      // Question analytics
      const questionAnalytics = results[0]?.assessment.questions.map(question => {
        const questionAnswers = results
          .map(r => (r.answers as QuizAnswer[]).find(a => a.questionId === question.id))
          .filter(Boolean);
        
        const correctAnswers = questionAnswers.filter(a => a?.isCorrect).length;
        
        return {
          questionId: question.id,
          question: question.question,
          correctRate: questionAnswers.length > 0 ? (correctAnswers / questionAnswers.length) * 100 : 0,
          totalAnswers: questionAnswers.length
        };
      }) || [];

      return {
        totalAttempts,
        averageScore,
        passRate,
        questionAnalytics
      };
    } catch (error) {
      console.error('Failed to get assessment analytics:', error);
      return {
        totalAttempts: 0,
        averageScore: 0,
        passRate: 0,
        questionAnalytics: []
      };
    }
  }

  /**
   * Check if an answer is correct
   */
  private checkAnswer(question: AssessmentQuestion, userAnswer: string): boolean {
    switch (question.type) {
      case 'MULTIPLE_CHOICE':
      case 'TRUE_FALSE':
        return userAnswer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
      
      case 'SHORT_ANSWER':
        // Simple string matching - in production, you might want fuzzy matching
        return userAnswer.toLowerCase().trim().includes(question.correctAnswer.toLowerCase().trim());
      
      case 'ESSAY':
        // Essays require manual grading - return false for now
        return false;
      
      default:
        return false;
    }
  }

  /**
   * Generate certificate for passed assessment
   */
  async generateCertificate(userId: string, courseId: string): Promise<string | null> {
    try {
      // Check if user has passed all required assessments
      const courseAssessments = await prisma.assessment.findMany({
        where: { courseId, isRequired: true }
      });

      const userResults = await prisma.assessmentResult.findMany({
        where: {
          userId,
          assessmentId: { in: courseAssessments.map(a => a.id) },
          passed: true
        }
      });

      if (userResults.length < courseAssessments.length) {
        return null; // Not all required assessments passed
      }

      // Generate unique certificate number
      const certificateNumber = `INNOHUB-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      
      // Create certificate record
      await prisma.certificate.create({
        data: {
          userId,
          courseId,
          certificateNumber,
          verificationHash: Buffer.from(certificateNumber).toString('base64'),
        }
      });

      return certificateNumber;
    } catch (error) {
      console.error('Failed to generate certificate:', error);
      return null;
    }
  }
}

export const assessmentService = new AssessmentService();
export default assessmentService;
