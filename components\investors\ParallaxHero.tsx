'use client';

import { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ParallaxHeroProps {
  className?: string;
}

export function ParallaxHero({ className }: ParallaxHeroProps) {
  const ref = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"]
  });

  // Simplified parallax effect - reduced complexity for better performance
  const contentY = useTransform(scrollYProgress, [0, 1], [0, 150]);
  const opacity = useTransform(scrollYProgress, [0, 0.6], [1, 0]);

  return (
    <div
      ref={ref}
      className={cn(
        "relative min-h-[90vh] flex flex-col items-center justify-center overflow-hidden",
        className
      )}
    >
      {/* BokehBackground with optimized settings */}
      <BokehBackground
        className="absolute inset-0 z-0"
        density={25}
        speed={1.2}
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9']}
      >
        <div className="hidden">Background effect</div>
      </BokehBackground>

      {/* Dark gradient overlay for better contrast */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/60 to-black/80 z-[1]" />

      {/* Grid overlay */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[length:50px_50px] z-[2]" />

      {/* Content with optimized animations */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto text-center">
          {/* Badge - immediate render with subtle animation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            style={{ y: contentY, opacity }}
          >
            <div className="inline-block rounded-full bg-primary/20 px-6 py-2 text-sm font-medium text-primary mb-8 backdrop-blur-sm border border-primary/20">
              Investor Network
            </div>
          </motion.div>

          {/* Main heading - immediate render with optimized animation */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1, ease: "easeOut" }}
            style={{ y: contentY, opacity }}
          >
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-8 text-white">
              Powering the Future of{' '}
              <span className="bg-gradient-to-r from-primary via-purple-400 to-primary bg-clip-text text-transparent">
                Innovation
              </span>
            </h1>
          </motion.div>

          {/* Description - immediate render */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            style={{ y: contentY, opacity }}
          >
            <p className="text-xl md:text-2xl text-white/80 mb-12 max-w-4xl mx-auto leading-relaxed">
              Join our exclusive network of forward-thinking investors and partners who are funding and supporting the next generation of groundbreaking startups.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-wrap gap-6 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-white backdrop-blur-sm border-0 px-8 py-4 text-lg font-medium"
                asChild
              >
                <Link href="#investor-companies">
                  Explore Our Network
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 text-lg font-medium"
                asChild
              >
                <Link href="#contact">
                  Join Our Network
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Optimized scroll indicator */}
      <motion.div
        className="absolute bottom-10 left-1/2 transform -translate-x-1/2 z-10"
        animate={{
          y: [0, 8, 0],
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="w-6 h-10 rounded-full border-2 border-white/40 flex items-start justify-center p-1 backdrop-blur-sm">
          <div className="w-1.5 h-3 bg-white/60 rounded-full" />
        </div>
      </motion.div>
    </div>
  );
}
