'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function SimpleTestPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAppwrite = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // Test environment variables first
      const config = {
        url: process.env.NEXT_PUBLIC_APPWRITE_URL,
        projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
        databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
      };
      
      setResult(`Environment Variables:
URL: ${config.url}
Project ID: ${config.projectId}
Database ID: ${config.databaseId}

Status: ${config.projectId ? 'Configuration OK' : 'Missing Project ID'}`);

      // Try to import Appwrite
      const { Client, Account } = await import('appwrite');
      
      const client = new Client();
      client
        .setEndpoint(config.url || 'https://cloud.appwrite.io/v1')
        .setProject(config.projectId || '');

      const account = new Account(client);
      
      // Test connection
      try {
        await account.get();
        setResult(prev => prev + '\n\nConnection: Success (User logged in)');
      } catch (error: any) {
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          setResult(prev => prev + '\n\nConnection: Success (No user logged in)');
        } else {
          setResult(prev => prev + `\n\nConnection Error: ${error.message}`);
        }
      }
      
    } catch (error: any) {
      setResult(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 p-4">
      <div className="max-w-2xl mx-auto">
        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-white">Simple Appwrite Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={testAppwrite}
              disabled={loading}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              {loading ? 'Testing...' : 'Test Appwrite Connection'}
            </Button>
            
            {result && (
              <div className="bg-gray-800/50 p-4 rounded-lg">
                <pre className="text-white text-sm whitespace-pre-wrap">{result}</pre>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
