# Create Appwrite Collections

Since the collections don't exist yet, you need to create them in your Appwrite console. Here's how:

## 1. Go to your Appwrite Console
- Open https://cloud.appwrite.io/
- Go to your project: `687498a80019bb7ec0d7`
- Navigate to "Databases" → Your database: `6874996000249ffe1438`

## 2. Create Collections

### Users Collection
1. Click "Create Collection"
2. Collection ID: `users`
3. Name: `Users`
4. Add these attributes:

```
email: string(255), required, unique
name: string(100), required  
role: enum[STUDENT,INSTRUCTOR,ADMIN], default: STUDENT
isOnboarded: boolean, default: false
avatar: string(500), optional
bio: string(1000), optional
skills: string[](50), optional
interests: string[](50), optional
createdAt: datetime, required
updatedAt: datetime, required
```

### Courses Collection
1. Click "Create Collection"
2. Collection ID: `courses`
3. Name: `Courses`
4. Add these attributes:

```
title: string(200), required
description: string(500), required
longDescription: string(5000), optional
thumbnail: string(500), optional
duration: string(50), required
level: enum[BEGINNER,INTERMEDIATE,ADVANCED]
category: string(100), required
instructor: string(100), required
instructorId: string(50), optional
price: integer, required
isPublished: boolean, default: false
tags: string[](50), optional
enrollmentCount: integer, default: 0
rating: float, default: 0
totalLessons: integer, default: 0
createdAt: datetime, required
updatedAt: datetime, required
```

### Blog Posts Collection
1. Click "Create Collection"
2. Collection ID: `blog_posts`
3. Name: `Blog Posts`
4. Add these attributes:

```
title: string(200), required
content: string(50000), required
excerpt: string(500), required
slug: string(250), required, unique
category: string(100), required
tags: string[](50), optional
isPublished: boolean, default: false
publishedAt: datetime, optional
authorId: string(50), required
authorName: string(100), required
authorEmail: string(255), required
views: integer, default: 0
readTime: integer, required
featuredImage: string(500), optional
metaDescription: string(300), optional
seoKeywords: string[](50), optional
createdAt: datetime, required
updatedAt: datetime, required
```

## 3. Set Permissions

For each collection, go to "Settings" → "Permissions":

### Users Collection Permissions:
- **Create**: Any authenticated user
- **Read**: Users can read their own data, admins can read all
- **Update**: Users can update their own data, admins can update all
- **Delete**: Only admins

### Courses Collection Permissions:
- **Create**: Instructors and admins
- **Read**: Any user (for published courses)
- **Update**: Course instructors and admins
- **Delete**: Only admins

### Blog Posts Collection Permissions:
- **Create**: Authors and admins
- **Read**: Any user (for published posts)
- **Update**: Post authors and admins
- **Delete**: Only admins

## 4. Quick Setup (Simplified)

For testing purposes, you can set all permissions to:
- **Create**: Any authenticated user
- **Read**: Any user
- **Update**: Any authenticated user  
- **Delete**: Any authenticated user

This allows full access for testing. You can restrict permissions later.

## 5. After Creating Collections

Once you've created the collections, run the test again:
- Go to http://localhost:3001/test-appwrite
- Click "Run All Tests"
- The "Collections Check" should now show success

## 6. Enable Account Creation

In your Appwrite console:
1. Go to "Auth" → "Settings"
2. Make sure "Account Creation" is enabled
3. Set appropriate security settings

After completing these steps, your Appwrite integration should work perfectly!
