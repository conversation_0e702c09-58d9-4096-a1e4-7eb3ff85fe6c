'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, CheckCircle, AlertCircle } from 'lucide-react';

export default function GrantAdminPage() {
  const { data: session, update } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleGrantAdmin = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/grant-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setResult({ success: true, message: 'Admin access granted successfully!' });
        // Update the session to reflect the new role
        await update();
        // Redirect to admin panel after a short delay
        setTimeout(() => {
          window.location.href = '/admin';
        }, 2000);
      } else {
        setResult({ success: false, message: data.error || 'Failed to grant admin access' });
      }
    } catch (error) {
      setResult({ success: false, message: 'An error occurred while granting admin access' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!session?.user) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <Card className="border-red-500/20 bg-red-500/5 max-w-md">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">Not Authenticated</h3>
            <p className="text-gray-400">Please log in first to grant admin access.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md max-w-md w-full">
        <CardHeader className="text-center">
          <Shield className="h-16 w-16 text-purple-400 mx-auto mb-4" />
          <CardTitle className="text-2xl text-white">Grant Admin Access</CardTitle>
          <p className="text-gray-400">
            Click the button below to grant admin privileges to your account.
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="bg-gray-800/30 p-4 rounded-lg">
            <h4 className="text-white font-medium mb-2">Current User:</h4>
            <p className="text-gray-300">{session.user.name}</p>
            <p className="text-gray-400 text-sm">{session.user.email}</p>
            <p className="text-purple-400 text-sm">Role: {session.user.role || 'STUDENT'}</p>
          </div>

          {result && (
            <div className={`p-4 rounded-lg border ${
              result.success 
                ? 'border-green-500/20 bg-green-500/5' 
                : 'border-red-500/20 bg-red-500/5'
            }`}>
              <div className="flex items-center gap-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-400" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-400" />
                )}
                <span className={result.success ? 'text-green-400' : 'text-red-400'}>
                  {result.message}
                </span>
              </div>
              {result.success && (
                <p className="text-gray-400 text-sm mt-2">
                  Redirecting to admin panel...
                </p>
              )}
            </div>
          )}

          <Button
            onClick={handleGrantAdmin}
            disabled={isLoading || session.user.role === 'ADMIN'}
            className="w-full bg-purple-500 hover:bg-purple-600 text-white"
          >
            {isLoading ? 'Granting Access...' : 
             session.user.role === 'ADMIN' ? 'Already Admin' : 
             'Grant Admin Access'}
          </Button>

          {session.user.role === 'ADMIN' && (
            <Button
              onClick={() => window.location.href = '/admin'}
              variant="outline"
              className="w-full border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
            >
              Go to Admin Panel
            </Button>
          )}

          <div className="text-center">
            <Button
              onClick={() => window.location.href = '/courses/dashboard'}
              variant="ghost"
              className="text-gray-400 hover:text-white"
            >
              Back to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
