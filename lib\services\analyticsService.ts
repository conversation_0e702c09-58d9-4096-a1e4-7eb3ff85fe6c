import { prisma } from '@/lib/prisma';

export interface DashboardMetrics {
  totalUsers: number;
  totalCourses: number;
  totalEnrollments: number;
  totalRevenue: number;
  activeUsers: number;
  completionRate: number;
  averageRating: number;
  growthRate: number;
}

export interface CourseAnalytics {
  courseId: string;
  title: string;
  enrollments: number;
  completions: number;
  completionRate: number;
  averageProgress: number;
  revenue: number;
  rating: number;
  totalWatchTime: number;
  dropoffPoints: { lessonId: string; dropoffRate: number }[];
}

export interface UserEngagement {
  dailyActiveUsers: { date: string; count: number }[];
  weeklyActiveUsers: { week: string; count: number }[];
  monthlyActiveUsers: { month: string; count: number }[];
  userRetention: { cohort: string; retention: number[] }[];
  sessionDuration: { average: number; median: number };
}

export interface RevenueAnalytics {
  totalRevenue: number;
  monthlyRevenue: { month: string; revenue: number; orders: number }[];
  revenueByPaymentMethod: { method: string; revenue: number; percentage: number }[];
  couponUsage: { code: string; uses: number; revenue: number }[];
  averageOrderValue: number;
  conversionRate: number;
}

export interface ContentAnalytics {
  popularCourses: { courseId: string; title: string; views: number; enrollments: number }[];
  contentEngagement: { type: string; averageTime: number; completionRate: number }[];
  searchQueries: { query: string; count: number; results: number }[];
  deviceBreakdown: { device: string; percentage: number }[];
}

class AnalyticsService {
  
  /**
   * Get dashboard overview metrics
   */
  async getDashboardMetrics(timeRange: '7d' | '30d' | '90d' | '1y' = '30d'): Promise<DashboardMetrics> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      const [
        totalUsers,
        totalCourses,
        totalEnrollments,
        activeUsers,
        completedEnrollments,
        previousPeriodUsers
      ] = await Promise.all([
        prisma.user.count(),
        prisma.course.count({ where: { isPublished: true } }),
        prisma.enrollment.count(),
        prisma.user.count({
          where: {
            updatedAt: { gte: startDate }
          }
        }),
        prisma.enrollment.count({
          where: { status: 'COMPLETED' }
        }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(startDate.getTime() - (endDate.getTime() - startDate.getTime())),
              lt: startDate
            }
          }
        })
      ]);

      const completionRate = totalEnrollments > 0 ? (completedEnrollments / totalEnrollments) * 100 : 0;
      const growthRate = previousPeriodUsers > 0 ? ((totalUsers - previousPeriodUsers) / previousPeriodUsers) * 100 : 0;

      return {
        totalUsers,
        totalCourses,
        totalEnrollments,
        totalRevenue: 0, // Will be calculated from payments table when implemented
        activeUsers,
        completionRate,
        averageRating: 4.5, // Placeholder - calculate from actual ratings
        growthRate
      };
    } catch (error) {
      console.error('Failed to get dashboard metrics:', error);
      return {
        totalUsers: 0,
        totalCourses: 0,
        totalEnrollments: 0,
        totalRevenue: 0,
        activeUsers: 0,
        completionRate: 0,
        averageRating: 0,
        growthRate: 0
      };
    }
  }

  /**
   * Get course performance analytics
   */
  async getCourseAnalytics(timeRange: '7d' | '30d' | '90d' | '1y' = '30d'): Promise<CourseAnalytics[]> {
    try {
      const courses = await prisma.course.findMany({
        where: { isPublished: true },
        include: {
          enrollments: {
            include: {
              user: true
            }
          },
          modules: {
            include: {
              lessons: true
            }
          }
        }
      });

      const analytics: CourseAnalytics[] = [];

      for (const course of courses) {
        const enrollments = course.enrollments.length;
        const completions = course.enrollments.filter(e => e.status === 'COMPLETED').length;
        const completionRate = enrollments > 0 ? (completions / enrollments) * 100 : 0;

        // Calculate average progress
        const allLessons = course.modules.flatMap(m => m.lessons);
        let totalProgress = 0;
        let progressCount = 0;

        for (const enrollment of course.enrollments) {
          const userProgress = await this.calculateUserCourseProgress(enrollment.userId, course.id);
          totalProgress += userProgress;
          progressCount++;
        }

        const averageProgress = progressCount > 0 ? totalProgress / progressCount : 0;

        analytics.push({
          courseId: course.id,
          title: course.title,
          enrollments,
          completions,
          completionRate,
          averageProgress,
          revenue: enrollments * (course.price || 0),
          rating: 4.5, // Placeholder
          totalWatchTime: 0, // Calculate from lesson progress
          dropoffPoints: [] // Analyze lesson completion rates
        });
      }

      return analytics.sort((a, b) => b.enrollments - a.enrollments);
    } catch (error) {
      console.error('Failed to get course analytics:', error);
      return [];
    }
  }

  /**
   * Get user engagement metrics
   */
  async getUserEngagement(timeRange: '7d' | '30d' | '90d' | '1y' = '30d'): Promise<UserEngagement> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Generate daily active users for the time range
      const dailyActiveUsers = [];
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const dayStart = new Date(currentDate);
        const dayEnd = new Date(currentDate);
        dayEnd.setDate(dayEnd.getDate() + 1);

        const activeCount = await prisma.user.count({
          where: {
            updatedAt: {
              gte: dayStart,
              lt: dayEnd
            }
          }
        });

        dailyActiveUsers.push({
          date: currentDate.toISOString().split('T')[0],
          count: activeCount
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      return {
        dailyActiveUsers,
        weeklyActiveUsers: [], // Aggregate daily data
        monthlyActiveUsers: [], // Aggregate daily data
        userRetention: [], // Calculate cohort retention
        sessionDuration: { average: 1800, median: 1200 } // Placeholder
      };
    } catch (error) {
      console.error('Failed to get user engagement:', error);
      return {
        dailyActiveUsers: [],
        weeklyActiveUsers: [],
        monthlyActiveUsers: [],
        userRetention: [],
        sessionDuration: { average: 0, median: 0 }
      };
    }
  }

  /**
   * Get revenue analytics
   */
  async getRevenueAnalytics(timeRange: '7d' | '30d' | '90d' | '1y' = '30d'): Promise<RevenueAnalytics> {
    try {
      // This would integrate with the payment system when implemented
      // For now, calculate based on enrollments and course prices
      
      const enrollments = await prisma.enrollment.findMany({
        include: {
          course: true
        }
      });

      const totalRevenue = enrollments.reduce((sum, enrollment) => {
        return sum + (enrollment.course.price || 0);
      }, 0);

      // Generate monthly revenue data
      const monthlyRevenue = [];
      const revenueByMonth = new Map<string, { revenue: number; orders: number }>();

      enrollments.forEach(enrollment => {
        const month = enrollment.enrolledAt.toISOString().substring(0, 7); // YYYY-MM
        const current = revenueByMonth.get(month) || { revenue: 0, orders: 0 };
        current.revenue += enrollment.course.price || 0;
        current.orders += 1;
        revenueByMonth.set(month, current);
      });

      revenueByMonth.forEach((data, month) => {
        monthlyRevenue.push({
          month,
          revenue: data.revenue,
          orders: data.orders
        });
      });

      const averageOrderValue = enrollments.length > 0 ? totalRevenue / enrollments.length : 0;

      return {
        totalRevenue,
        monthlyRevenue: monthlyRevenue.sort((a, b) => a.month.localeCompare(b.month)),
        revenueByPaymentMethod: [
          { method: 'QPay', revenue: totalRevenue * 0.4, percentage: 40 },
          { method: 'Khan Bank', revenue: totalRevenue * 0.3, percentage: 30 },
          { method: 'SocialPay', revenue: totalRevenue * 0.2, percentage: 20 },
          { method: 'Other', revenue: totalRevenue * 0.1, percentage: 10 }
        ],
        couponUsage: [], // Calculate from coupon redemptions
        averageOrderValue,
        conversionRate: 15.5 // Placeholder
      };
    } catch (error) {
      console.error('Failed to get revenue analytics:', error);
      return {
        totalRevenue: 0,
        monthlyRevenue: [],
        revenueByPaymentMethod: [],
        couponUsage: [],
        averageOrderValue: 0,
        conversionRate: 0
      };
    }
  }

  /**
   * Get content analytics
   */
  async getContentAnalytics(): Promise<ContentAnalytics> {
    try {
      const courses = await prisma.course.findMany({
        where: { isPublished: true },
        include: {
          enrollments: true
        }
      });

      const popularCourses = courses
        .map(course => ({
          courseId: course.id,
          title: course.title,
          views: course.enrollments.length * 3, // Estimate views
          enrollments: course.enrollments.length
        }))
        .sort((a, b) => b.enrollments - a.enrollments)
        .slice(0, 10);

      return {
        popularCourses,
        contentEngagement: [
          { type: 'Video Lessons', averageTime: 1200, completionRate: 85 },
          { type: 'Assessments', averageTime: 600, completionRate: 78 },
          { type: 'Reading Materials', averageTime: 300, completionRate: 65 }
        ],
        searchQueries: [
          { query: 'entrepreneurship', count: 150, results: 12 },
          { query: 'marketing', count: 120, results: 8 },
          { query: 'business plan', count: 95, results: 15 }
        ],
        deviceBreakdown: [
          { device: 'Mobile', percentage: 65 },
          { device: 'Desktop', percentage: 30 },
          { device: 'Tablet', percentage: 5 }
        ]
      };
    } catch (error) {
      console.error('Failed to get content analytics:', error);
      return {
        popularCourses: [],
        contentEngagement: [],
        searchQueries: [],
        deviceBreakdown: []
      };
    }
  }

  /**
   * Calculate user course progress
   */
  private async calculateUserCourseProgress(userId: string, courseId: string): Promise<number> {
    try {
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          modules: {
            include: {
              lessons: true
            }
          }
        }
      });

      if (!course) return 0;

      const allLessons = course.modules.flatMap(module => module.lessons);
      const totalLessons = allLessons.length;

      if (totalLessons === 0) return 0;

      const completedProgress = await prisma.lessonProgress.findMany({
        where: {
          userId,
          lessonId: { in: allLessons.map(lesson => lesson.id) },
          isCompleted: true
        }
      });

      const completedLessons = completedProgress.length;
      return (completedLessons / totalLessons) * 100;
    } catch (error) {
      console.error('Failed to calculate user course progress:', error);
      return 0;
    }
  }

  /**
   * Export analytics data
   */
  async exportAnalytics(type: 'courses' | 'users' | 'revenue' | 'engagement', format: 'csv' | 'json' = 'csv'): Promise<string> {
    try {
      let data: any[] = [];

      switch (type) {
        case 'courses':
          data = await this.getCourseAnalytics();
          break;
        case 'users':
          const userEngagement = await this.getUserEngagement();
          data = userEngagement.dailyActiveUsers;
          break;
        case 'revenue':
          const revenueData = await this.getRevenueAnalytics();
          data = revenueData.monthlyRevenue;
          break;
        case 'engagement':
          const engagement = await this.getUserEngagement();
          data = engagement.dailyActiveUsers;
          break;
      }

      if (format === 'json') {
        return JSON.stringify(data, null, 2);
      } else {
        // Convert to CSV
        if (data.length === 0) return '';
        
        const headers = Object.keys(data[0]).join(',');
        const rows = data.map(row => Object.values(row).join(','));
        return [headers, ...rows].join('\n');
      }
    } catch (error) {
      console.error('Failed to export analytics:', error);
      return '';
    }
  }
}

export const analyticsService = new AnalyticsService();
export default analyticsService;
