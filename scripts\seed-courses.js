const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedCourses() {
  try {
    console.log('🌱 Seeding courses...');

    // Create sample courses
    const courses = [
      {
        id: '1',
        title: 'Entrepreneurship Fundamentals',
        description: 'Learn the basics of starting and running a successful business',
        longDescription: 'This comprehensive course covers everything you need to know about entrepreneurship, from idea generation to business planning and execution.',
        thumbnail: '/images/courses/entrepreneurship.jpg',
        duration: '8 hours',
        level: 'BEGINNER',
        category: 'Business',
        instructor: 'Dr. Batbayar',
        price: 50000,
        isPublished: true
      },
      {
        id: '2',
        title: 'Digital Marketing Mastery',
        description: 'Master digital marketing strategies for the modern business',
        longDescription: 'Learn how to create effective digital marketing campaigns, understand social media marketing, SEO, and online advertising.',
        thumbnail: '/images/courses/digital-marketing.jpg',
        duration: '12 hours',
        level: 'INTERMEDIATE',
        category: 'Marketing',
        instructor: 'Oyunaa Ganbaatar',
        price: 75000,
        isPublished: true
      },
      {
        id: '3',
        title: 'Product Development & Innovation',
        description: 'Build innovative products that customers love',
        longDescription: 'Discover the product development process, from ideation to launch, including user research, prototyping, and market validation.',
        thumbnail: '/images/courses/product-development.jpg',
        duration: '10 hours',
        level: 'ADVANCED',
        category: 'Product',
        instructor: 'Munkh-Erdene Bold',
        price: 100000,
        isPublished: true
      }
    ];

    for (const courseData of courses) {
      // Check if course already exists
      const existingCourse = await prisma.course.findUnique({
        where: { id: courseData.id }
      });

      if (existingCourse) {
        console.log(`✅ Course "${courseData.title}" already exists`);
        continue;
      }

      // Create course
      const course = await prisma.course.create({
        data: courseData
      });

      console.log(`✅ Created course: ${course.title}`);

      // Create modules and lessons for each course
      const modules = [
        {
          title: 'Introduction',
          order: 1,
          lessons: [
            {
              title: 'Welcome to the Course',
              description: 'Course overview and what you\'ll learn',
              videoId: 'dQw4w9WgXcQ', // Sample YouTube video ID
              duration: '5:30',
              order: 1
            },
            {
              title: 'Course Materials',
              description: 'Download course materials and resources',
              videoId: 'dQw4w9WgXcQ',
              duration: '3:15',
              order: 2
            }
          ]
        },
        {
          title: 'Core Concepts',
          order: 2,
          lessons: [
            {
              title: 'Fundamental Principles',
              description: 'Understanding the core principles',
              videoId: 'dQw4w9WgXcQ',
              duration: '15:45',
              order: 1
            },
            {
              title: 'Practical Applications',
              description: 'How to apply what you\'ve learned',
              videoId: 'dQw4w9WgXcQ',
              duration: '20:30',
              order: 2
            }
          ]
        },
        {
          title: 'Advanced Topics',
          order: 3,
          lessons: [
            {
              title: 'Advanced Strategies',
              description: 'Advanced techniques and strategies',
              videoId: 'dQw4w9WgXcQ',
              duration: '25:00',
              order: 1
            },
            {
              title: 'Case Studies',
              description: 'Real-world case studies and examples',
              videoId: 'dQw4w9WgXcQ',
              duration: '18:20',
              order: 2
            }
          ]
        }
      ];

      for (const moduleData of modules) {
        const module = await prisma.module.create({
          data: {
            title: moduleData.title,
            order: moduleData.order,
            courseId: course.id
          }
        });

        for (const lessonData of moduleData.lessons) {
          await prisma.lesson.create({
            data: {
              ...lessonData,
              moduleId: module.id
            }
          });
        }
      }

      console.log(`✅ Created modules and lessons for: ${course.title}`);
    }

    console.log('🎉 Course seeding completed!');
    
    // List all courses
    const allCourses = await prisma.course.findMany({
      include: {
        modules: {
          include: {
            lessons: true
          }
        }
      }
    });

    console.log('\n📚 Courses in database:');
    allCourses.forEach(course => {
      console.log(`- ${course.title} (${course.modules.length} modules, ${course.modules.reduce((sum, m) => sum + m.lessons.length, 0)} lessons)`);
    });

  } catch (error) {
    console.error('❌ Error seeding courses:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedCourses();
