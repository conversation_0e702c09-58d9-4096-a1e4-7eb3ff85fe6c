import { Home, Briefcase, Users, TrendingUp, Newspaper } from 'lucide-react'
import { NavBar } from "@/components/ui/tubelight-navbar"

export function NavBarDemo() {
  const navItems = [
    { name: 'Home', url: '/', icon: Home },
    { name: 'Programs', url: '/programs', icon: Briefcase },
    { name: 'Success Stories', url: '/success-stories', icon: TrendingUp },
    { name: 'Team', url: '/team', icon: Users },
    { name: 'News', url: '/news', icon: Newspaper }
  ]

  return <NavBar items={navItems} />
}

// Alternative demo with InnoHub specific navigation
export function InnoHubNavBarDemo() {
  const navItems = [
    { name: 'Home', url: '/', icon: Home },
    { name: 'Programs', url: '/programs', icon: Briefcase },
    { name: 'Investors', url: '/investors', icon: TrendingUp },
    { name: 'News', url: '/news', icon: Newspaper }
  ]

  return <NavBar items={navItems} />
}
