import { Client, Account, Databases, Storage, Teams } from 'appwrite';

// Appwrite configuration function to get fresh env vars
export const getAppwriteConfig = () => ({
  url: process.env.NEXT_PUBLIC_APPWRITE_URL || 'https://cloud.appwrite.io/v1',
  projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '',
  databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '',
  storageId: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_ID || '',

  // Collection IDs
  usersCollectionId: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID || '',
  coursesCollectionId: process.env.NEXT_PUBLIC_APPWRITE_COURSES_COLLECTION_ID || '',
  lessonsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_LESSONS_COLLECTION_ID || '',
  enrollmentsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_ENROLLMENTS_COLLECTION_ID || '',
  blogPostsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_BLOG_POSTS_COLLECTION_ID || '',
  progressCollectionId: process.env.NEXT_PUBLIC_APPWRITE_PROGRESS_COLLECTION_ID || '',
  couponsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_COUPONS_COLLECTION_ID || '',
});

// Export static config for backward compatibility
export const appwriteConfig = getAppwriteConfig();

// Lazy client initialization
let client: Client | null = null;
let account: Account | null = null;
let databases: Databases | null = null;
let storage: Storage | null = null;
let teams: Teams | null = null;

const initializeClient = () => {
  if (!client) {
    const config = getAppwriteConfig();
    client = new Client();
    client
      .setEndpoint(config.url)
      .setProject(config.projectId);

    account = new Account(client);
    databases = new Databases(client);
    storage = new Storage(client);
    teams = new Teams(client);
  }
  return { client, account, databases, storage, teams };
};

// Export getters that initialize client on first use
export const getAccount = () => {
  const { account } = initializeClient();
  return account!;
};

export const getDatabases = () => {
  const { databases } = initializeClient();
  return databases!;
};

export const getStorage = () => {
  const { storage } = initializeClient();
  return storage!;
};

export const getTeams = () => {
  const { teams } = initializeClient();
  return teams!;
};

export const getClient = () => {
  const { client } = initializeClient();
  return client!;
};

// For backward compatibility, export direct instances (but they'll be lazy-loaded)
export { getAccount as account, getDatabases as databases, getStorage as storage, getTeams as teams };

export default getClient;
