const { Client, Databases, Permission, Role } = require('node-appwrite');

// Configuration
const client = new Client();
const databases = new Databases(client);

client
  .setEndpoint('https://syd.cloud.appwrite.io/v1')
  .setProject('687498a80019bb7ec0d7')
  .setKey('YOUR_API_KEY_HERE'); // You'll need to get this from Appwrite console

const databaseId = '6874996000249ffe1438';

async function createCollections() {
  try {
    console.log('🚀 Starting collection creation...');

    // 1. Create Users Collection
    console.log('📝 Creating Users collection...');
    try {
      const usersCollection = await databases.createCollection(
        databaseId,
        'users',
        'Users'
      );
      console.log('✅ Users collection created');

      // Add attributes to Users collection
      await databases.createStringAttribute(databaseId, 'users', 'email', 255, true);
      await databases.createStringAttribute(databaseId, 'users', 'name', 100, true);
      await databases.createEnumAttribute(databaseId, 'users', 'role', ['STUDENT', 'INSTRUCTOR', 'ADMIN'], true, 'STUDENT');
      await databases.createBooleanAttribute(databaseId, 'users', 'isOnboarded', true, false);
      await databases.createStringAttribute(databaseId, 'users', 'avatar', 500, false);
      await databases.createStringAttribute(databaseId, 'users', 'bio', 1000, false);
      await databases.createStringAttribute(databaseId, 'users', 'skills', 50, false, null, true);
      await databases.createStringAttribute(databaseId, 'users', 'interests', 50, false, null, true);
      await databases.createDatetimeAttribute(databaseId, 'users', 'createdAt', true);
      await databases.createDatetimeAttribute(databaseId, 'users', 'updatedAt', true);
      
      console.log('✅ Users attributes created');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ Users collection already exists');
      } else {
        throw error;
      }
    }

    // 2. Create Courses Collection
    console.log('📝 Creating Courses collection...');
    try {
      await databases.createCollection(databaseId, 'courses', 'Courses');
      console.log('✅ Courses collection created');

      // Add attributes to Courses collection
      await databases.createStringAttribute(databaseId, 'courses', 'title', 200, true);
      await databases.createStringAttribute(databaseId, 'courses', 'description', 500, true);
      await databases.createStringAttribute(databaseId, 'courses', 'longDescription', 5000, false);
      await databases.createStringAttribute(databaseId, 'courses', 'thumbnail', 500, false);
      await databases.createStringAttribute(databaseId, 'courses', 'duration', 50, true);
      await databases.createEnumAttribute(databaseId, 'courses', 'level', ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'], true);
      await databases.createStringAttribute(databaseId, 'courses', 'category', 100, true);
      await databases.createStringAttribute(databaseId, 'courses', 'instructor', 100, true);
      await databases.createStringAttribute(databaseId, 'courses', 'instructorId', 50, false);
      await databases.createIntegerAttribute(databaseId, 'courses', 'price', true);
      await databases.createBooleanAttribute(databaseId, 'courses', 'isPublished', true, false);
      await databases.createStringAttribute(databaseId, 'courses', 'tags', 50, false, null, true);
      await databases.createIntegerAttribute(databaseId, 'courses', 'enrollmentCount', true, 0);
      await databases.createFloatAttribute(databaseId, 'courses', 'rating', true, 0);
      await databases.createIntegerAttribute(databaseId, 'courses', 'totalLessons', true, 0);
      await databases.createDatetimeAttribute(databaseId, 'courses', 'createdAt', true);
      await databases.createDatetimeAttribute(databaseId, 'courses', 'updatedAt', true);
      
      console.log('✅ Courses attributes created');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ Courses collection already exists');
      } else {
        throw error;
      }
    }

    // 3. Create Blog Posts Collection
    console.log('📝 Creating Blog Posts collection...');
    try {
      await databases.createCollection(databaseId, 'blog_posts', 'Blog Posts');
      console.log('✅ Blog Posts collection created');

      // Add attributes to Blog Posts collection
      await databases.createStringAttribute(databaseId, 'blog_posts', 'title', 200, true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'content', 50000, true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'excerpt', 500, true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'slug', 250, true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'category', 100, true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'tags', 50, false, null, true);
      await databases.createBooleanAttribute(databaseId, 'blog_posts', 'isPublished', true, false);
      await databases.createDatetimeAttribute(databaseId, 'blog_posts', 'publishedAt', false);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'authorId', 50, true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'authorName', 100, true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'authorEmail', 255, true);
      await databases.createIntegerAttribute(databaseId, 'blog_posts', 'views', true, 0);
      await databases.createIntegerAttribute(databaseId, 'blog_posts', 'readTime', true);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'featuredImage', 500, false);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'metaDescription', 300, false);
      await databases.createStringAttribute(databaseId, 'blog_posts', 'seoKeywords', 50, false, null, true);
      await databases.createDatetimeAttribute(databaseId, 'blog_posts', 'createdAt', true);
      await databases.createDatetimeAttribute(databaseId, 'blog_posts', 'updatedAt', true);
      
      console.log('✅ Blog Posts attributes created');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️ Blog Posts collection already exists');
      } else {
        throw error;
      }
    }

    console.log('🎉 All collections created successfully!');
    console.log('\nNext steps:');
    console.log('1. Set up permissions in Appwrite console');
    console.log('2. Test the collections with the test page');
    console.log('3. Update admin components to use real data');

  } catch (error) {
    console.error('❌ Error creating collections:', error.message);
    console.log('\nManual setup required. Please create collections manually in Appwrite console.');
  }
}

// Run the script
createCollections();
