import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import emailService from '@/lib/services/emailService';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, data } = body;

    let success = false;

    switch (type) {
      case 'welcome':
        success = await emailService.sendWelcomeEmail(data.user);
        break;
        
      case 'enrollment':
        success = await emailService.sendEnrollmentConfirmation(data.user, data.course);
        break;
        
      case 'certificate':
        success = await emailService.sendCertificate(data.user, data.course, data.certificateNumber);
        break;
        
      case 'progress_reminder':
        success = await emailService.sendProgressReminder(data.user, data.course);
        break;
        
      case 'coupon':
        success = await emailService.sendCouponNotification(data.user, data.coupon);
        break;
        
      case 'payment_confirmation':
        success = await emailService.sendPaymentConfirmation(data.user, data.payment);
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid email type' },
          { status: 400 }
        );
    }

    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
