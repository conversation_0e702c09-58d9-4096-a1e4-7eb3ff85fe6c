import { ID, Query } from 'appwrite';
import { databases, appwriteConfig } from './config';

export interface AppwriteBlogPost {
  $id: string;
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  category: string;
  tags: string[];
  isPublished: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  authorId: string;
  authorName: string;
  authorEmail: string;
  views: number;
  readTime: number;
  featuredImage?: string;
  metaDescription?: string;
  seoKeywords?: string[];
}

export interface CreateBlogPostData {
  title: string;
  content: string;
  excerpt: string;
  category: string;
  tags: string[];
  isPublished?: boolean;
  publishedAt?: string;
  authorId: string;
  authorName: string;
  authorEmail: string;
  featuredImage?: string;
  metaDescription?: string;
  seoKeywords?: string[];
}

export interface UpdateBlogPostData {
  title?: string;
  content?: string;
  excerpt?: string;
  category?: string;
  tags?: string[];
  isPublished?: boolean;
  publishedAt?: string;
  featuredImage?: string;
  metaDescription?: string;
  seoKeywords?: string[];
}

export interface BlogPostFilters {
  page?: number;
  limit?: number;
  category?: string;
  tag?: string;
  search?: string;
  published?: boolean;
  sortBy?: 'createdAt' | 'publishedAt' | 'views' | 'title';
  sortOrder?: 'asc' | 'desc';
}

class AppwriteBlogService {
  // Generate slug from title
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  // Calculate read time based on content
  private calculateReadTime(content: string): number {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  // Create a new blog post
  async createPost(postData: CreateBlogPostData): Promise<AppwriteBlogPost> {
    try {
      const slug = this.generateSlug(postData.title);
      const readTime = this.calculateReadTime(postData.content);

      const post = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        ID.unique(),
        {
          ...postData,
          slug,
          readTime,
          views: 0,
          isPublished: postData.isPublished || false,
          publishedAt: postData.isPublished ? (postData.publishedAt || new Date().toISOString()) : null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
      return post as AppwriteBlogPost;
    } catch (error) {
      console.error('Error creating blog post:', error);
      throw error;
    }
  }

  // Get all blog posts with filters
  async getAllPosts(filters: BlogPostFilters = {}): Promise<{
    posts: AppwriteBlogPost[];
    total: number;
    pages: number;
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        category,
        tag,
        search,
        published,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      const queries = [];
      
      // Add filters
      if (published !== undefined) {
        queries.push(Query.equal('isPublished', published));
      }
      
      if (category) {
        queries.push(Query.equal('category', category));
      }
      
      if (tag) {
        queries.push(Query.contains('tags', tag));
      }
      
      if (search) {
        queries.push(Query.search('title', search));
      }

      // Add sorting
      if (sortOrder === 'desc') {
        queries.push(Query.orderDesc(sortBy));
      } else {
        queries.push(Query.orderAsc(sortBy));
      }

      // Add pagination
      const offset = (page - 1) * limit;
      queries.push(Query.limit(limit));
      queries.push(Query.offset(offset));

      const posts = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        queries
      );

      // Get total count for pagination
      const totalQuery = queries.filter(q => 
        !q.toString().includes('limit') && 
        !q.toString().includes('offset') &&
        !q.toString().includes('orderDesc') &&
        !q.toString().includes('orderAsc')
      );
      
      const totalResult = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        totalQuery
      );

      return {
        posts: posts.documents as AppwriteBlogPost[],
        total: totalResult.total,
        pages: Math.ceil(totalResult.total / limit)
      };
    } catch (error) {
      console.error('Error getting blog posts:', error);
      return { posts: [], total: 0, pages: 0 };
    }
  }

  // Get blog post by ID or slug
  async getPost(identifier: string, incrementViews = false): Promise<AppwriteBlogPost | null> {
    try {
      let post: AppwriteBlogPost | null = null;

      // Try to get by ID first
      try {
        const postById = await databases.getDocument(
          appwriteConfig.databaseId,
          appwriteConfig.blogPostsCollectionId,
          identifier
        );
        post = postById as AppwriteBlogPost;
      } catch {
        // If ID fails, try to get by slug
        const postsBySlug = await databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.blogPostsCollectionId,
          [Query.equal('slug', identifier)]
        );
        
        if (postsBySlug.documents.length > 0) {
          post = postsBySlug.documents[0] as AppwriteBlogPost;
        }
      }

      // Increment views if requested
      if (post && incrementViews) {
        await databases.updateDocument(
          appwriteConfig.databaseId,
          appwriteConfig.blogPostsCollectionId,
          post.$id,
          { views: post.views + 1 }
        );
        post.views += 1;
      }

      return post;
    } catch (error) {
      console.error('Error getting blog post:', error);
      return null;
    }
  }

  // Update blog post
  async updatePost(postId: string, updateData: UpdateBlogPostData): Promise<AppwriteBlogPost> {
    try {
      const updates: any = {
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      // Update slug if title changed
      if (updateData.title) {
        updates.slug = this.generateSlug(updateData.title);
      }

      // Update read time if content changed
      if (updateData.content) {
        updates.readTime = this.calculateReadTime(updateData.content);
      }

      // Set published date if publishing
      if (updateData.isPublished && !updateData.publishedAt) {
        updates.publishedAt = new Date().toISOString();
      }

      const updatedPost = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        postId,
        updates
      );
      return updatedPost as AppwriteBlogPost;
    } catch (error) {
      console.error('Error updating blog post:', error);
      throw error;
    }
  }

  // Delete blog post
  async deletePost(postId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        postId
      );
    } catch (error) {
      console.error('Error deleting blog post:', error);
      throw error;
    }
  }

  // Get posts by category
  async getPostsByCategory(category: string): Promise<AppwriteBlogPost[]> {
    try {
      const posts = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        [
          Query.equal('category', category),
          Query.equal('isPublished', true),
          Query.orderDesc('publishedAt')
        ]
      );
      return posts.documents as AppwriteBlogPost[];
    } catch (error) {
      console.error('Error getting posts by category:', error);
      return [];
    }
  }

  // Get posts by tag
  async getPostsByTag(tag: string): Promise<AppwriteBlogPost[]> {
    try {
      const posts = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        [
          Query.contains('tags', tag),
          Query.equal('isPublished', true),
          Query.orderDesc('publishedAt')
        ]
      );
      return posts.documents as AppwriteBlogPost[];
    } catch (error) {
      console.error('Error getting posts by tag:', error);
      return [];
    }
  }

  // Get popular posts
  async getPopularPosts(limit = 5): Promise<AppwriteBlogPost[]> {
    try {
      const posts = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        [
          Query.equal('isPublished', true),
          Query.orderDesc('views'),
          Query.limit(limit)
        ]
      );
      return posts.documents as AppwriteBlogPost[];
    } catch (error) {
      console.error('Error getting popular posts:', error);
      return [];
    }
  }

  // Get recent posts
  async getRecentPosts(limit = 5): Promise<AppwriteBlogPost[]> {
    try {
      const posts = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.blogPostsCollectionId,
        [
          Query.equal('isPublished', true),
          Query.orderDesc('publishedAt'),
          Query.limit(limit)
        ]
      );
      return posts.documents as AppwriteBlogPost[];
    } catch (error) {
      console.error('Error getting recent posts:', error);
      return [];
    }
  }
}

export const appwriteBlogService = new AppwriteBlogService();
export default appwriteBlogService;
