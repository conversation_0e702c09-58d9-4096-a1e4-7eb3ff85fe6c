'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useLanguage } from '@/lib/context/language-context';
import ProtectedRoute from '@/components/courses/ProtectedRoute';
import CoursesNavbar from '@/components/courses/CoursesNavbar';
import VideoPlayer from '@/components/courses/VideoPlayer';
import courseService from '@/lib/services/courseService';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  Clock, 
  Award, 
  ThumbsUp, 
  MessageSquare, 
  Share2, 
  Download, 
  Bookmark,
  ChevronLeft,
  PlayCircle,
  CheckCircle
} from 'lucide-react';

// Course data with diverse entrepreneurship content
const COURSES_DATA = [
  {
    id: 1,
    title: 'Entrepreneurship Fundamentals',
    description: 'Learn the fundamentals of starting and growing a successful business with real-world insights',
    longDescription: 'This comprehensive course features real conversations and insights from successful entrepreneurs and industry leaders. You\'ll learn about the entrepreneurial journey, challenges, and opportunities in the modern business landscape. Our content is based on actual experiences from the InnoHub Accelerator Center community.',
    thumbnail: '/images/courses/entrepreneurship.jpg',
    duration: '1h 29m',
    lessons: 8,
    level: 'Beginner',
    instructor: 'Otgonbaatar Munkhjin',
    instructorTitle: 'Founder & CEO, InnoHub Accelerator Center',
    instructorImage: '/images/instructors/otgonbaatar.jpg',
    category: 'Business',
    videoId: 'hlCYcATi0m4',
    modules: [
      {
        title: 'Entrepreneurial Journey',
        lessons: [
          { title: 'CarPodcast: Founder Story', duration: '25:30', isCompleted: true, videoId: 'hlCYcATi0m4' },
          { title: 'Building a Startup Ecosystem', duration: '20:15', isCompleted: true, videoId: 'esjWT-D9OF8' },
          { title: 'Innovation Hub Insights', duration: '15:45', isCompleted: false, videoId: 'hlCYcATi0m4' },
        ]
      },
      {
        title: 'Business Development',
        lessons: [
          { title: 'Market Validation Strategies', duration: '18:30', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Product-Market Fit', duration: '22:10', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Scaling Your Business', duration: '19:25', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
      {
        title: 'Advanced Topics',
        lessons: [
          { title: 'Investment & Funding', duration: '24:15', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Building Strategic Partnerships', duration: '16:40', isCompleted: false, videoId: 'esjWT-D9OF8' },
        ]
      },
    ],
    progress: 25,
  },
  {
    id: 2,
    title: 'Innovation Hub Insights',
    description: 'Discover how innovation hubs work and their role in startup ecosystem development',
    longDescription: 'Get exclusive insights into how innovation hubs operate and contribute to the startup ecosystem. Learn from real experiences and case studies from the InnoHub Accelerator Center.',
    thumbnail: '/images/courses/innovation.jpg',
    duration: '45m',
    lessons: 6,
    level: 'Beginner',
    instructor: 'InnoHub Team',
    instructorTitle: 'Innovation Hub Experts',
    instructorImage: '/images/instructors/team.jpg',
    category: 'Business',
    videoId: 'esjWT-D9OF8',
    modules: [
      {
        title: 'Hub Operations',
        lessons: [
          { title: 'Innovation Hub Overview', duration: '12:30', isCompleted: true, videoId: 'esjWT-D9OF8' },
          { title: 'Community Building', duration: '15:10', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Mentorship Programs', duration: '10:25', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
        ]
      },
      {
        title: 'Ecosystem Impact',
        lessons: [
          { title: 'Success Stories', duration: '18:15', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Future of Innovation', duration: '12:40', isCompleted: false, videoId: 'gElfIo6uw4g' },
          { title: 'Global Connections', duration: '8:55', isCompleted: false, videoId: 'M966MmhOBT0' },
        ]
      },
    ],
    progress: 16,
  },
  {
    id: 3,
    title: 'Startup Funding Masterclass',
    description: 'Complete guide to raising capital and securing investment for your startup',
    longDescription: 'Master the art of fundraising with this comprehensive course covering everything from pitch decks to investor relations. Learn proven strategies used by successful startups to secure funding.',
    thumbnail: '/images/courses/funding.jpg',
    duration: '18m',
    lessons: 5,
    level: 'Intermediate',
    instructor: 'Investment Experts',
    instructorTitle: 'Venture Capital Partners',
    instructorImage: '/images/instructors/investment.jpg',
    category: 'Finance',
    videoId: 'dQw4w9WgXcQ',
    modules: [
      {
        title: 'Funding Fundamentals',
        lessons: [
          { title: 'Types of Startup Funding', duration: '8:30', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Preparing for Investment', duration: '6:15', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Pitch Deck Essentials', duration: '7:45', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
      {
        title: 'Investor Relations',
        lessons: [
          { title: 'Finding the Right Investors', duration: '5:30', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Negotiating Terms', duration: '4:20', isCompleted: false, videoId: 'hlCYcATi0m4' },
        ]
      },
    ],
    progress: 0,
  },
  {
    id: 4,
    title: 'Digital Marketing for Startups',
    description: 'Master digital marketing strategies to grow your startup on a budget',
    longDescription: 'Learn how to build a strong digital presence for your startup without breaking the bank. This course covers social media marketing, content creation, SEO basics, and growth hacking techniques specifically designed for early-stage companies.',
    thumbnail: '/images/courses/marketing.jpg',
    duration: '2h 15m',
    lessons: 12,
    level: 'Beginner',
    instructor: 'Sarah Chen',
    instructorTitle: 'Digital Marketing Strategist',
    instructorImage: '/images/instructors/sarah.jpg',
    category: 'Marketing',
    videoId: 'gElfIo6uw4g',
    modules: [
      {
        title: 'Marketing Fundamentals',
        lessons: [
          { title: 'Digital Marketing Overview', duration: '15:30', isCompleted: false, videoId: 'gElfIo6uw4g' },
          { title: 'Understanding Your Target Audience', duration: '18:45', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Building Your Brand Identity', duration: '22:10', isCompleted: false, videoId: 'esjWT-D9OF8' },
          { title: 'Content Marketing Strategy', duration: '20:30', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
        ]
      },
      {
        title: 'Social Media Marketing',
        lessons: [
          { title: 'Platform Selection Strategy', duration: '16:20', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Creating Engaging Content', duration: '25:15', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Social Media Analytics', duration: '19:40', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
      {
        title: 'Growth Hacking',
        lessons: [
          { title: 'SEO Basics for Startups', duration: '24:30', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Email Marketing Automation', duration: '21:45', isCompleted: false, videoId: 'esjWT-D9OF8' },
          { title: 'Viral Marketing Techniques', duration: '18:20', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Measuring Marketing ROI', duration: '17:30', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Scaling Your Marketing Efforts', duration: '23:15', isCompleted: false, videoId: 'M966MmhOBT0' },
        ]
      },
    ],
    progress: 0,
  },
  {
    id: 5,
    title: 'Product Development & Design',
    description: 'From idea to market: comprehensive product development methodology',
    longDescription: 'Learn the complete product development lifecycle from ideation to launch. This course covers user research, prototyping, design thinking, agile development, and product-market fit validation.',
    thumbnail: '/images/courses/product.jpg',
    duration: '3h 45m',
    lessons: 15,
    level: 'Intermediate',
    instructor: 'Alex Rodriguez',
    instructorTitle: 'Senior Product Manager',
    instructorImage: '/images/instructors/alex.jpg',
    category: 'Product',
    videoId: 'M966MmhOBT0',
    modules: [
      {
        title: 'Product Strategy',
        lessons: [
          { title: 'Product Vision & Strategy', duration: '28:30', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Market Research & Analysis', duration: '32:15', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Competitive Analysis', duration: '25:40', isCompleted: false, videoId: 'esjWT-D9OF8' },
          { title: 'User Personas & Journey Mapping', duration: '30:20', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
        ]
      },
      {
        title: 'Design & Prototyping',
        lessons: [
          { title: 'Design Thinking Process', duration: '26:45', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Wireframing & Mockups', duration: '24:30', isCompleted: false, videoId: 'gElfIo6uw4g' },
          { title: 'Prototyping Tools & Techniques', duration: '29:15', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'User Testing & Feedback', duration: '27:50', isCompleted: false, videoId: 'hlCYcATi0m4' },
        ]
      },
      {
        title: 'Development & Launch',
        lessons: [
          { title: 'Agile Development Methodology', duration: '31:20', isCompleted: false, videoId: 'esjWT-D9OF8' },
          { title: 'MVP Development Strategy', duration: '28:45', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Quality Assurance & Testing', duration: '22:30', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Product Launch Strategy', duration: '26:15', isCompleted: false, videoId: 'gElfIo6uw4g' },
          { title: 'Post-Launch Optimization', duration: '24:40', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Scaling Your Product', duration: '30:10', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Product Analytics & Metrics', duration: '25:30', isCompleted: false, videoId: 'esjWT-D9OF8' },
        ]
      },
    ],
    progress: 0,
  },
  {
    id: 6,
    title: 'Building Successful Teams',
    description: 'Learn how to recruit, manage, and scale high-performing startup teams',
    longDescription: 'Building a great team is crucial for startup success. This course covers recruitment strategies, team dynamics, leadership skills, and creating a positive company culture that attracts and retains top talent.',
    thumbnail: '/images/courses/teams.jpg',
    duration: '1h 45m',
    lessons: 10,
    level: 'Intermediate',
    instructor: 'Michael Johnson',
    instructorTitle: 'HR Director & Team Building Expert',
    instructorImage: '/images/instructors/michael.jpg',
    category: 'Business',
    videoId: 'jNQXAC9IVRw',
    modules: [
      {
        title: 'Recruitment & Hiring',
        lessons: [
          { title: 'Defining Team Roles & Responsibilities', duration: '12:30', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Effective Recruitment Strategies', duration: '15:45', isCompleted: false, videoId: 'gElfIo6uw4g' },
          { title: 'Interview Techniques & Assessment', duration: '18:20', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Onboarding New Team Members', duration: '14:15', isCompleted: false, videoId: 'hlCYcATi0m4' },
        ]
      },
      {
        title: 'Team Management',
        lessons: [
          { title: 'Leadership Styles & Approaches', duration: '16:30', isCompleted: false, videoId: 'esjWT-D9OF8' },
          { title: 'Communication & Collaboration', duration: '13:45', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Performance Management', duration: '17:20', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Conflict Resolution', duration: '12:40', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
      {
        title: 'Company Culture',
        lessons: [
          { title: 'Building Company Culture', duration: '19:15', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Employee Retention Strategies', duration: '11:30', isCompleted: false, videoId: 'hlCYcATi0m4' },
        ]
      },
    ],
    progress: 0,
  },
  {
    id: 7,
    title: 'Legal Basics for Startups',
    description: 'Essential legal knowledge every entrepreneur needs to protect their business',
    longDescription: 'Navigate the legal landscape of entrepreneurship with confidence. This course covers business formation, intellectual property protection, contracts, compliance requirements, and common legal pitfalls to avoid.',
    thumbnail: '/images/courses/legal.jpg',
    duration: '1h 30m',
    lessons: 8,
    level: 'Beginner',
    instructor: 'Jennifer Lee',
    instructorTitle: 'Startup Attorney & Legal Advisor',
    instructorImage: '/images/instructors/jennifer.jpg',
    category: 'Business',
    videoId: 'hlCYcATi0m4',
    modules: [
      {
        title: 'Business Formation',
        lessons: [
          { title: 'Choosing Business Structure', duration: '14:30', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Registration & Licensing', duration: '12:45', isCompleted: false, videoId: 'esjWT-D9OF8' },
          { title: 'Tax Considerations', duration: '16:20', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
        ]
      },
      {
        title: 'Intellectual Property',
        lessons: [
          { title: 'Trademarks & Copyrights', duration: '18:15', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Patent Basics', duration: '15:30', isCompleted: false, videoId: 'gElfIo6uw4g' },
          { title: 'Trade Secrets Protection', duration: '11:45', isCompleted: false, videoId: 'M966MmhOBT0' },
        ]
      },
      {
        title: 'Contracts & Compliance',
        lessons: [
          { title: 'Contract Fundamentals', duration: '13:20', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Employment Law Basics', duration: '17:40', isCompleted: false, videoId: 'esjWT-D9OF8' },
        ]
      },
    ],
    progress: 0,
  },
  {
    id: 8,
    title: 'Scaling Your Business',
    description: 'Strategies and frameworks for scaling your startup to the next level',
    longDescription: 'Learn how to scale your startup effectively without losing quality or company culture. This course covers growth strategies, operational efficiency, international expansion, and managing rapid growth challenges.',
    thumbnail: '/images/courses/scaling.jpg',
    duration: '2h 30m',
    lessons: 11,
    level: 'Advanced',
    instructor: 'David Park',
    instructorTitle: 'Growth Strategy Consultant',
    instructorImage: '/images/instructors/david.jpg',
    category: 'Business',
    videoId: 'esjWT-D9OF8',
    modules: [
      {
        title: 'Growth Strategy',
        lessons: [
          { title: 'Scaling vs Growing: Key Differences', duration: '16:30', isCompleted: false, videoId: 'esjWT-D9OF8' },
          { title: 'Market Expansion Strategies', duration: '22:15', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Product Line Extension', duration: '18:45', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Strategic Partnerships', duration: '20:30', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
      {
        title: 'Operational Excellence',
        lessons: [
          { title: 'Process Optimization', duration: '19:20', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Technology & Automation', duration: '24:15', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Quality Management Systems', duration: '17:40', isCompleted: false, videoId: 'esjWT-D9OF8' },
        ]
      },
      {
        title: 'Managing Growth',
        lessons: [
          { title: 'Organizational Structure for Scale', duration: '21:30', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Culture Preservation During Growth', duration: '15:45', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'International Expansion', duration: '26:20', isCompleted: false, videoId: 'gElfIo6uw4g' },
          { title: 'Exit Strategies & IPO Preparation', duration: '23:10', isCompleted: false, videoId: 'M966MmhOBT0' },
        ]
      },
    ],
    progress: 0,
  },
  {
    id: 9,
    title: 'Financial Management',
    description: 'Essential financial skills every entrepreneur needs to know',
    longDescription: 'Master the financial aspects of running a startup. Learn about financial planning, cash flow management, investment analysis, and financial reporting to make informed business decisions.',
    thumbnail: '/images/courses/finance.jpg',
    duration: '2h 10m',
    lessons: 9,
    level: 'Intermediate',
    instructor: 'Financial Advisors',
    instructorTitle: 'Certified Financial Planners',
    instructorImage: '/images/instructors/finance-team.jpg',
    category: 'Finance',
    videoId: 'dQw4w9WgXcQ',
    modules: [
      {
        title: 'Financial Planning',
        lessons: [
          { title: 'Financial Statements Overview', duration: '18:30', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Budgeting & Forecasting', duration: '22:15', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Cash Flow Management', duration: '20:45', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
      {
        title: 'Investment & Funding',
        lessons: [
          { title: 'Investment Analysis', duration: '24:20', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Valuation Methods', duration: '19:30', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Due Diligence Process', duration: '17:15', isCompleted: false, videoId: 'esjWT-D9OF8' },
        ]
      },
      {
        title: 'Financial Control',
        lessons: [
          { title: 'Risk Management', duration: '16:40', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Financial Reporting', duration: '14:30', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Tax Planning Strategies', duration: '21:20', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
    ],
    progress: 0,
  },
  {
    id: 10,
    title: 'Innovation Methodologies',
    description: 'Learn proven innovation frameworks and methodologies for business growth',
    longDescription: 'Discover cutting-edge innovation methodologies used by successful companies worldwide. Learn design thinking, lean startup methodology, agile innovation, and how to foster a culture of innovation.',
    thumbnail: '/images/courses/innovation-methods.jpg',
    duration: '1h 50m',
    lessons: 7,
    level: 'Advanced',
    instructor: 'Innovation Consultants',
    instructorTitle: 'Innovation Strategy Experts',
    instructorImage: '/images/instructors/innovation-team.jpg',
    category: 'Product',
    videoId: 'M966MmhOBT0',
    modules: [
      {
        title: 'Innovation Frameworks',
        lessons: [
          { title: 'Design Thinking Methodology', duration: '25:30', isCompleted: false, videoId: 'M966MmhOBT0' },
          { title: 'Lean Startup Principles', duration: '22:15', isCompleted: false, videoId: 'hlCYcATi0m4' },
          { title: 'Agile Innovation Process', duration: '19:45', isCompleted: false, videoId: 'esjWT-D9OF8' },
        ]
      },
      {
        title: 'Innovation Culture',
        lessons: [
          { title: 'Building Innovation Teams', duration: '18:20', isCompleted: false, videoId: 'dQw4w9WgXcQ' },
          { title: 'Fostering Creative Thinking', duration: '16:30', isCompleted: false, videoId: 'jNQXAC9IVRw' },
          { title: 'Innovation Metrics & KPIs', duration: '14:40', isCompleted: false, videoId: 'gElfIo6uw4g' },
        ]
      },
      {
        title: 'Implementation',
        lessons: [
          { title: 'From Idea to Market', duration: '23:15', isCompleted: false, videoId: 'M966MmhOBT0' },
        ]
      },
    ],
    progress: 0,
  },
];

export default function CoursePage() {
  const { t, isLoaded } = useLanguage();
  const params = useParams();
  const courseId = Number(params.id);
  
  const [course, setCourse] = useState<typeof COURSES_DATA[0] | null>(null);
  const [currentVideo, setCurrentVideo] = useState<string | null>(null);
  const [currentLessonTitle, setCurrentLessonTitle] = useState<string>('');
  const [selectedLesson, setSelectedLesson] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const loadCourse = async () => {
      try {
        setIsLoading(true);
        const courseData = await courseService.getCourseById(courseId.toString());
        if (courseData) {
          setCourse(courseData);
          // Set the first video as the current video
          if (courseData.modules.length > 0 && courseData.modules[0].lessons.length > 0) {
            const firstLesson = courseData.modules[0].lessons[0];
            setCurrentVideo(firstLesson.videoId);
            setCurrentLessonTitle(firstLesson.title);
            setSelectedLesson(firstLesson);
          }
        }
      } catch (error) {
        console.error('Failed to load course:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCourse();
  }, [courseId]);
  
  const handleLessonClick = (lesson: any) => {
    setCurrentVideo(lesson.videoId);
    setCurrentLessonTitle(lesson.title);
    setSelectedLesson(lesson);
    // Scroll to top on mobile
    if (window.innerWidth < 768) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };
  
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="min-h-screen bg-black pt-24 pb-16 flex items-center justify-center">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
            <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
            <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
          </div>
        </div>
      );
    }

    if (!course) {
      return (
        <div className="min-h-screen bg-black pt-24 pb-16 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">
              {isLoaded ? t('courses.notFound') : 'Course not found'}
            </h2>
            <p className="text-muted-foreground mb-6">
              {isLoaded ? t('courses.notFoundDesc') : 'The course you are looking for does not exist or has been removed.'}
            </p>
            <Button asChild>
              <Link href="/courses">
                <ChevronLeft className="mr-2 h-4 w-4" />
                {isLoaded ? t('courses.backToCourses') : 'Back to Courses'}
              </Link>
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="min-h-screen bg-gradient-to-b from-[#10001F] to-black text-white">
        <CoursesNavbar />
        <div className="container mx-auto px-4 py-24 lg:py-32">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="w-full lg:w-2/3">
              {/* Video Player */}
              <div className="aspect-video mb-4">
                {currentVideo ? (
                  <VideoPlayer
                    videoId={currentVideo}
                    lessonId={selectedLesson?.id || ''}
                    title={currentLessonTitle}
                    onProgress={(progress) => {
                      // Handle progress updates
                      console.log('Video progress:', progress);
                    }}
                    onComplete={() => {
                      // Handle lesson completion
                      console.log('Lesson completed');
                    }}
                    className="w-full h-full rounded-lg shadow-lg shadow-primary/20"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-black rounded-lg">
                    <p className="text-gray-400">Select a lesson to start</p>
                  </div>
                )}
              </div>

              {/* Video Details & Actions */}
              <div className="bg-black/40 border border-primary/10 rounded-lg p-4">
                <h1 className="text-2xl font-bold">{currentLessonTitle}</h1>
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <ThumbsUp className="h-4 w-4" />
                      <span>{isLoaded ? t('courses.like') : 'Like'}</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      <span>{isLoaded ? t('courses.comment') : 'Comment'}</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <Share2 className="h-4 w-4" />
                      <span>{isLoaded ? t('courses.share') : 'Share'}</span>
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <Bookmark className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <Tabs defaultValue="about" className="p-4">
                <TabsList className="bg-background/5 mb-4">
                  <TabsTrigger
                    value="about"
                    className="data-[state=active]:bg-primary/20"
                  >
                    {isLoaded ? t('courses.about') : 'About'}
                  </TabsTrigger>
                  <TabsTrigger
                    value="instructor"
                    className="data-[state=active]:bg-primary/20"
                  >
                    {isLoaded ? t('courses.instructor') : 'Instructor'}
                  </TabsTrigger>
                  <TabsTrigger
                    value="resources"
                    className="data-[state=active]:bg-primary/20"
                  >
                    {isLoaded ? t('courses.resources') : 'Resources'}
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="about">
                  <div className="space-y-4">
                    <div className="flex flex-wrap gap-4 mb-4">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>{course.duration}</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <BookOpen className="h-4 w-4 mr-2" />
                        <span>{course.lessons} {isLoaded ? t('courses.lessons') : 'lessons'}</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Award className="h-4 w-4 mr-2" />
                        <span>{course.level}</span>
                      </div>
                    </div>
                    
                    <h3 className="text-lg font-semibold">
                      {isLoaded ? t('courses.description') : 'Description'}
                    </h3>
                    <p className="text-muted-foreground">{course.longDescription}</p>
                    
                    <h3 className="text-lg font-semibold mt-6">
                      {isLoaded ? t('courses.whatYouWillLearn') : 'What You Will Learn'}
                    </h3>
                    <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Understand the fundamentals of entrepreneurship</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Develop a business idea from concept to execution</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Create a comprehensive business plan</span>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
                        <span>Understand different funding options for startups</span>
                      </li>
                    </ul>
                  </div>
                </TabsContent>
                
                <TabsContent value="instructor">
                  <div className="flex items-start gap-4">
                    <div className="relative w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
                      <Image
                        src={course.instructorImage}
                        alt={course.instructor}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://placehold.co/200x200/3a0647/ffffff?text=Instructor';
                        }}
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{course.instructor}</h3>
                      <p className="text-sm text-muted-foreground">{course.instructorTitle}</p>
                      <p className="mt-2 text-muted-foreground">
                        An experienced entrepreneur and educator with over 10 years of experience in the startup ecosystem.
                        Passionate about helping new entrepreneurs navigate the challenges of starting a business.
                      </p>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="resources">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">
                      {isLoaded ? t('courses.downloadableResources') : 'Downloadable Resources'}
                    </h3>
                    <ul className="space-y-2">
                      <li>
                        <Button variant="link" className="p-0 h-auto text-primary">
                          <Download className="h-4 w-4 mr-2" />
                          Business Plan Template.pdf
                        </Button>
                      </li>
                      <li>
                        <Button variant="link" className="p-0 h-auto text-primary">
                          <Download className="h-4 w-4 mr-2" />
                          Market Research Worksheet.xlsx
                        </Button>
                      </li>
                      <li>
                        <Button variant="link" className="p-0 h-auto text-primary">
                          <Download className="h-4 w-4 mr-2" />
                          Financial Projections Template.xlsx
                        </Button>
                      </li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Course Content Section */}
            <div className="w-full lg:w-1/3">
              <div className="bg-black/40 border border-primary/10 rounded-lg overflow-hidden">
                <div className="p-4 border-b border-primary/10">
                  <h2 className="text-xl font-semibold">
                    {isLoaded ? t('courses.courseContent') : 'Course Content'}
                  </h2>
                  <div className="flex items-center justify-between mt-2">
                    <div className="text-sm text-muted-foreground">
                      {course.progress}% {isLoaded ? t('courses.completed') : 'completed'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {course.modules.reduce((acc, module) => acc + module.lessons.length, 0)} {isLoaded ? t('courses.lessons') : 'lessons'}
                    </div>
                  </div>
                  <Progress value={course.progress} className="mt-2 h-2" />
                </div>
                
                <div className="max-h-[600px] overflow-y-auto">
                  {course.modules.map((module, moduleIndex) => (
                    <div key={moduleIndex} className="border-b border-primary/10 last:border-b-0">
                      <div className="p-4 font-medium">
                        {module.title}
                      </div>
                      <div>
                        {module.lessons.map((lesson, lessonIndex) => (
                          <button
                            key={lessonIndex}
                            className={`w-full text-left p-4 hover:bg-primary/5 flex items-start gap-3 transition-colors ${currentLessonTitle === lesson.title ? 'bg-primary/10' : ''}`}
                            onClick={() => handleLessonClick(lesson)}
                          >
                            {/* Video Thumbnail */}
                            <div className="relative w-16 h-12 rounded-lg overflow-hidden bg-muted/20 flex-shrink-0">
                              <Image
                                src={`https://img.youtube.com/vi/${lesson.videoId}/mqdefault.jpg`}
                                alt={lesson.title}
                                fill
                                className="object-cover"
                                onError={(e) => {
                                  // Fallback for missing thumbnails
                                  const target = e.target as HTMLImageElement;
                                  target.src = 'https://placehold.co/320x180/3a0647/ffffff?text=Video';
                                }}
                              />
                              {/* Play overlay */}
                              <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                                <div className={`rounded-full p-1 ${lesson.isCompleted ? 'bg-primary/80 text-white' : 'bg-white/80 text-black'}`}>
                                  {lesson.isCompleted ? (
                                    <CheckCircle className="h-3 w-3" />
                                  ) : (
                                    <PlayCircle className="h-3 w-3" />
                                  )}
                                </div>
                              </div>
                              {/* Duration badge */}
                              <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 py-0.5 rounded">
                                {lesson.duration}
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="text-sm font-medium">{lesson.title}</div>
                              <div className="text-xs text-muted-foreground mt-1">
                                {lesson.isCompleted ? 'Completed' : 'Not started'}
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return <ProtectedRoute>{renderContent()}</ProtectedRoute>;
}
