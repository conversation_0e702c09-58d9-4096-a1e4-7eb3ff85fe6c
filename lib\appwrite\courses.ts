import { ID, Query } from 'appwrite';
import { databases, appwriteConfig } from './config';

export interface AppwriteCourse {
  $id: string;
  title: string;
  description: string;
  longDescription?: string;
  thumbnail?: string;
  duration: string;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  category: string;
  instructor: string;
  instructorId?: string;
  price: number;
  isPublished: boolean;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  enrollmentCount?: number;
  rating?: number;
  totalLessons?: number;
}

export interface AppwriteLesson {
  $id: string;
  courseId: string;
  title: string;
  description?: string;
  videoId?: string;
  videoUrl?: string;
  duration: string;
  order: number;
  isPublished: boolean;
  content?: string;
  resources?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface AppwriteEnrollment {
  $id: string;
  userId: string;
  courseId: string;
  enrolledAt: string;
  completedAt?: string;
  progress: number;
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
}

export interface CreateCourseData {
  title: string;
  description: string;
  longDescription?: string;
  thumbnail?: string;
  duration: string;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  category: string;
  instructor: string;
  instructorId?: string;
  price: number;
  isPublished?: boolean;
  tags?: string[];
}

export interface CreateLessonData {
  courseId: string;
  title: string;
  description?: string;
  videoId?: string;
  videoUrl?: string;
  duration: string;
  order: number;
  isPublished?: boolean;
  content?: string;
  resources?: string[];
}

class AppwriteCourseService {
  // Create a new course
  async createCourse(courseData: CreateCourseData): Promise<AppwriteCourse> {
    try {
      const course = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.coursesCollectionId,
        ID.unique(),
        {
          ...courseData,
          isPublished: courseData.isPublished || false,
          enrollmentCount: 0,
          rating: 0,
          totalLessons: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
      return course as AppwriteCourse;
    } catch (error) {
      console.error('Error creating course:', error);
      throw error;
    }
  }

  // Get all published courses
  async getAllCourses(): Promise<AppwriteCourse[]> {
    try {
      const courses = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.coursesCollectionId,
        [Query.equal('isPublished', true), Query.orderDesc('createdAt')]
      );
      return courses.documents as AppwriteCourse[];
    } catch (error) {
      console.error('Error getting courses:', error);
      return [];
    }
  }

  // Get all courses (admin)
  async getAllCoursesAdmin(): Promise<AppwriteCourse[]> {
    try {
      const courses = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.coursesCollectionId,
        [Query.orderDesc('createdAt')]
      );
      return courses.documents as AppwriteCourse[];
    } catch (error) {
      console.error('Error getting all courses:', error);
      return [];
    }
  }

  // Get course by ID
  async getCourseById(courseId: string): Promise<AppwriteCourse | null> {
    try {
      const course = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.coursesCollectionId,
        courseId
      );
      return course as AppwriteCourse;
    } catch (error) {
      console.error('Error getting course:', error);
      return null;
    }
  }

  // Update course
  async updateCourse(courseId: string, updateData: Partial<CreateCourseData>): Promise<AppwriteCourse> {
    try {
      const updatedCourse = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.coursesCollectionId,
        courseId,
        {
          ...updateData,
          updatedAt: new Date().toISOString(),
        }
      );
      return updatedCourse as AppwriteCourse;
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  }

  // Delete course
  async deleteCourse(courseId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        appwriteConfig.databaseId,
        appwriteConfig.coursesCollectionId,
        courseId
      );
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  }

  // Create lesson
  async createLesson(lessonData: CreateLessonData): Promise<AppwriteLesson> {
    try {
      const lesson = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.lessonsCollectionId,
        ID.unique(),
        {
          ...lessonData,
          isPublished: lessonData.isPublished || false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
      return lesson as AppwriteLesson;
    } catch (error) {
      console.error('Error creating lesson:', error);
      throw error;
    }
  }

  // Get lessons for a course
  async getLessonsByCourse(courseId: string): Promise<AppwriteLesson[]> {
    try {
      const lessons = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.lessonsCollectionId,
        [Query.equal('courseId', courseId), Query.orderAsc('order')]
      );
      return lessons.documents as AppwriteLesson[];
    } catch (error) {
      console.error('Error getting lessons:', error);
      return [];
    }
  }

  // Enroll user in course
  async enrollUser(userId: string, courseId: string): Promise<AppwriteEnrollment> {
    try {
      const enrollment = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.enrollmentsCollectionId,
        ID.unique(),
        {
          userId,
          courseId,
          enrolledAt: new Date().toISOString(),
          progress: 0,
          status: 'ACTIVE',
        }
      );
      return enrollment as AppwriteEnrollment;
    } catch (error) {
      console.error('Error enrolling user:', error);
      throw error;
    }
  }

  // Get user enrollments
  async getUserEnrollments(userId: string): Promise<AppwriteEnrollment[]> {
    try {
      const enrollments = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.enrollmentsCollectionId,
        [Query.equal('userId', userId)]
      );
      return enrollments.documents as AppwriteEnrollment[];
    } catch (error) {
      console.error('Error getting user enrollments:', error);
      return [];
    }
  }

  // Search courses
  async searchCourses(query: string, filters?: {
    category?: string;
    level?: string;
    priceRange?: [number, number];
  }): Promise<AppwriteCourse[]> {
    try {
      const queries = [Query.equal('isPublished', true)];
      
      if (query) {
        queries.push(Query.search('title', query));
      }
      
      if (filters?.category && filters.category !== 'All') {
        queries.push(Query.equal('category', filters.category));
      }
      
      if (filters?.level && filters.level !== 'All') {
        queries.push(Query.equal('level', filters.level));
      }

      const courses = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.coursesCollectionId,
        queries
      );
      
      let results = courses.documents as AppwriteCourse[];
      
      // Filter by price range if specified
      if (filters?.priceRange) {
        results = results.filter(course => 
          course.price >= filters.priceRange![0] && 
          course.price <= filters.priceRange![1]
        );
      }
      
      return results;
    } catch (error) {
      console.error('Error searching courses:', error);
      return [];
    }
  }
}

export const appwriteCourseService = new AppwriteCourseService();
export default appwriteCourseService;
