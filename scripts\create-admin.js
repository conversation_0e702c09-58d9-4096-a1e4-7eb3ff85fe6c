const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      // Update existing user to admin
      const updatedUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { role: 'ADMIN' }
      });
      console.log('✅ Updated existing user to admin:', updatedUser.email);
    } else {
      // Create new admin user (NextAuth.js will handle password via credentials provider)
      const adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'ADMIN'
        }
      });
      console.log('✅ Created new admin user:', adminUser.email);
      console.log('ℹ️  Note: Use the signup form to set a password for this user');
    }

    // List all users
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true
      }
    });

    console.log('\n📋 All users in database:');
    allUsers.forEach(user => {
      console.log(`- ${user.email} (${user.name}) - Role: ${user.role}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
