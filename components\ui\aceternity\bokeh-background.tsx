'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useMobile, useMobilePerformance } from '@/hooks/use-mobile';

interface BokehProps {
  children: React.ReactNode;
  className?: string;
  density?: number;
  speed?: number;
  colors?: string[];
}

export const BokehBackground = ({
  children,
  className,
  density = 35, // Optimized default density
  speed = 2.5, // Faster for snappier movement
  colors = ['#ffcc33', '#ffaa00', '#ff8800', '#ffffff'],
}: BokehProps) => {
  const [bokehElements, setBokehElements] = useState<React.ReactNode[]>([]);
  const { isMobile } = useMobile();
  const { shouldReduceMotion } = useMobilePerformance();

  // Mobile-optimized density and speed
  const mobileDensity = isMobile ? Math.floor(density * 0.4) : density;
  const mobileSpeed = isMobile ? speed * 1.5 : speed;

  useEffect(() => {
    const elements = [];

    // Further reduced layers on mobile for better performance
    const layerCount = isMobile ? 1 : 2;
    for (let layer = 0; layer < layerCount; layer++) {
      const layerDensity = Math.floor(mobileDensity / layerCount);

      // Simplified blur classes
      const layerBlur = layer === 0 ? 'blur-sm' : 'blur-md';

      // Simplified opacity multiplier
      const layerOpacityMultiplier = layer === 0 ? 1 : 0.6;

      for (let i = 0; i < layerDensity; i++) {
        const size = isMobile
          ? Math.random() * 40 + 20  // 20-60px for mobile
          : Math.random() * 80 + 40; // 40-120px for desktop
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const delay = Math.random() * (isMobile ? 2 : 4); // Shorter delay on mobile
        const duration = (Math.random() * 8 + 12) / mobileSpeed; // Use mobile-optimized speed
        const color = colors[Math.floor(Math.random() * colors.length)];
        const baseOpacity = (Math.random() * 0.3 + 0.15) * layerOpacityMultiplier; // 0.15-0.45 adjusted by layer

        // Mobile-optimized movement patterns
        const movementRange = isMobile
          ? 20 + Math.random() * 10  // 20-30px for mobile
          : 40 + Math.random() * 20; // 40-60px for desktop

        elements.push(
          <motion.div
            key={`${layer}-${i}`}
            className={`absolute rounded-full ${layerBlur}`}
            style={{
              width: `${size}px`,
              height: `${size}px`,
              left: `${x}%`,
              top: `${y}%`,
              backgroundColor: color,
              opacity: baseOpacity,
            }}
            animate={shouldReduceMotion ? {} : {
              x: [-movementRange/2, movementRange/2, -movementRange/2],
              y: [-movementRange/3, movementRange/3, -movementRange/3],
              opacity: [baseOpacity, baseOpacity * 1.5, baseOpacity],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration,
              repeat: Infinity,
              repeatType: 'loop',
              delay,
              ease: "easeInOut",
            }}
          />
        );
      }
    }

    // Mobile-optimized large floating elements
    const largeElementCount = isMobile ? Math.floor(mobileDensity / 25) : Math.floor(mobileDensity / 15);
    for (let i = 0; i < largeElementCount; i++) {
      const size = isMobile
        ? Math.random() * 60 + 40   // 40-100px for mobile
        : Math.random() * 120 + 80; // 80-200px for desktop
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      const delay = Math.random() * (isMobile ? 3 : 6);
      const duration = (Math.random() * 15 + 20) / mobileSpeed;
      const color = colors[Math.floor(Math.random() * colors.length)];
      const opacity = Math.random() * 0.12 + 0.08; // Slightly more visible

      elements.push(
        <motion.div
          key={`large-${i}`}
          className={`absolute rounded-full ${isMobile ? 'blur-xl' : 'blur-2xl'}`}
          style={{
            width: `${size}px`,
            height: `${size}px`,
            left: `${x}%`,
            top: `${y}%`,
            backgroundColor: color,
            opacity,
          }}
          animate={shouldReduceMotion ? {} : {
            x: isMobile ? [-10, 10, -10] : [-20, 20, -20],
            y: isMobile ? [-8, 8, -8] : [-15, 15, -15],
            opacity: [opacity, opacity * 1.8, opacity],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration,
            repeat: Infinity,
            repeatType: 'loop',
            delay,
            ease: "easeInOut",
          }}
        />
      );
    }

    setBokehElements(elements);
  }, [mobileDensity, mobileSpeed, colors, isMobile, shouldReduceMotion]);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <div className="absolute inset-0 z-0">
        {bokehElements}
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-10" />
      </div>
      <div className="relative z-20">{children}</div>
    </div>
  );
};

export const GlassCard = ({
  children,
  className,
  hoverEffect = true,
}: {
  children: React.ReactNode;
  className?: string;
  hoverEffect?: boolean;
}) => {
  const { isMobile } = useMobile();

  return (
    <motion.div
      className={cn(
        'relative backdrop-blur-md bg-white/5 border border-white/10 rounded-xl overflow-hidden',
        hoverEffect && 'transition-all duration-200 hover:bg-white/8 hover:shadow-md',
        isMobile && 'active:bg-white/10 active:scale-95', // Touch feedback for mobile
        className
      )}
      initial={{ opacity: 0, y: 15 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      viewport={{ once: true }}
      whileTap={isMobile ? { scale: 0.98 } : undefined} // Touch feedback
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/8 to-transparent opacity-40" />
      <div className="relative z-10">{children}</div>
    </motion.div>
  );
};
