'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON><PERSON>, XCircle, Loader2 } from 'lucide-react';

export default function BasicTestPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [testUser, setTestUser] = useState({
    email: '<EMAIL>',
    password: 'testpassword123',
    name: 'Test User'
  });

  const testBasicConnection = async () => {
    setLoading(true);
    setResult('Testing basic Appwrite connection...\n\n');
    
    try {
      // Test environment variables
      const config = {
        url: process.env.NEXT_PUBLIC_APPWRITE_URL,
        projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
        databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
      };
      
      setResult(prev => prev + `✅ Environment Variables Loaded:
URL: ${config.url}
Project ID: ${config.projectId}
Database ID: ${config.databaseId}

`);

      // Import and test Appwrite
      const { Client, Account } = await import('appwrite');
      
      const client = new Client();
      client
        .setEndpoint(config.url || 'https://cloud.appwrite.io/v1')
        .setProject(config.projectId || '');

      const account = new Account(client);
      
      setResult(prev => prev + '✅ Appwrite Client Initialized\n\n');
      
      // Test connection (this will fail if not logged in, which is expected)
      try {
        const user = await account.get();
        setResult(prev => prev + `✅ Connection Success - User logged in: ${user.email}\n\n`);
      } catch (error: any) {
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          setResult(prev => prev + '✅ Connection Success - No user logged in (expected)\n\n');
        } else {
          setResult(prev => prev + `❌ Connection Error: ${error.message}\n\n`);
        }
      }
      
      setResult(prev => prev + '🎉 Basic Appwrite integration is working!\n\n');
      setResult(prev => prev + 'Next steps:\n');
      setResult(prev => prev + '1. Create collections in Appwrite console\n');
      setResult(prev => prev + '2. Set up proper permissions\n');
      setResult(prev => prev + '3. Test user registration\n');
      
    } catch (error: any) {
      setResult(prev => prev + `❌ Error: ${error.message}\n`);
    } finally {
      setLoading(false);
    }
  };

  const testUserRegistration = async () => {
    setLoading(true);
    setResult('Testing user registration...\n\n');
    
    try {
      const { Client, Account, ID } = await import('appwrite');
      
      const config = {
        url: process.env.NEXT_PUBLIC_APPWRITE_URL,
        projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
      };
      
      const client = new Client();
      client
        .setEndpoint(config.url || 'https://cloud.appwrite.io/v1')
        .setProject(config.projectId || '');

      const account = new Account(client);
      
      // Try to create account
      const newAccount = await account.create(
        ID.unique(),
        testUser.email,
        testUser.password,
        testUser.name
      );
      
      setResult(prev => prev + `✅ User account created successfully!
User ID: ${newAccount.$id}
Email: ${newAccount.email}
Name: ${newAccount.name}

`);
      
      // Try to login
      const session = await account.createEmailPasswordSession(testUser.email, testUser.password);
      setResult(prev => prev + `✅ Login successful!
Session ID: ${session.$id}

`);
      
      // Get current user
      const currentUser = await account.get();
      setResult(prev => prev + `✅ Current user retrieved:
Email: ${currentUser.email}
Verified: ${currentUser.emailVerification}

`);
      
      setResult(prev => prev + '🎉 User registration and login working perfectly!\n');
      
    } catch (error: any) {
      if (error.message.includes('user_already_exists')) {
        setResult(prev => prev + `ℹ️ User already exists. Trying to login...\n\n`);
        
        try {
          const { Client, Account } = await import('appwrite');
          const config = {
            url: process.env.NEXT_PUBLIC_APPWRITE_URL,
            projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
          };
          
          const client = new Client();
          client
            .setEndpoint(config.url || 'https://cloud.appwrite.io/v1')
            .setProject(config.projectId || '');

          const account = new Account(client);
          
          const session = await account.createEmailPasswordSession(testUser.email, testUser.password);
          setResult(prev => prev + `✅ Login successful with existing user!\n`);
          
        } catch (loginError: any) {
          setResult(prev => prev + `❌ Login failed: ${loginError.message}\n`);
        }
      } else {
        setResult(prev => prev + `❌ Registration failed: ${error.message}\n`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 p-4">
      <div className="max-w-4xl mx-auto">
        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md mb-6">
          <CardHeader>
            <CardTitle className="text-white">Basic Appwrite Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={testBasicConnection}
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                Test Basic Connection
              </Button>
              
              <Button
                onClick={testUserRegistration}
                disabled={loading}
                variant="outline"
                className="border-purple-500/50 text-purple-400 hover:bg-purple-500/10"
              >
                {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                Test User Registration
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div>
                <Label className="text-gray-300">Email</Label>
                <Input
                  value={testUser.email}
                  onChange={(e) => setTestUser({...testUser, email: e.target.value})}
                  className="bg-gray-800 border-gray-600 text-white"
                />
              </div>
              <div>
                <Label className="text-gray-300">Password</Label>
                <Input
                  type="password"
                  value={testUser.password}
                  onChange={(e) => setTestUser({...testUser, password: e.target.value})}
                  className="bg-gray-800 border-gray-600 text-white"
                />
              </div>
              <div>
                <Label className="text-gray-300">Name</Label>
                <Input
                  value={testUser.name}
                  onChange={(e) => setTestUser({...testUser, name: e.target.value})}
                  className="bg-gray-800 border-gray-600 text-white"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {result && (
          <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-white">Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-800/50 p-4 rounded-lg">
                <pre className="text-white text-sm whitespace-pre-wrap font-mono">{result}</pre>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
