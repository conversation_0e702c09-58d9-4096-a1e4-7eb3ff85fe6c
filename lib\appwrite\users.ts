import { ID, Query } from 'appwrite';
import { getAccount, getDatabases, getAppwriteConfig } from './config';

export interface AppwriteUser {
  $id: string;
  email: string;
  name: string;
  role: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN';
  isOnboarded: boolean;
  createdAt: string;
  updatedAt: string;
  avatar?: string;
  bio?: string;
  skills?: string[];
  interests?: string[];
}

export interface CreateUserData {
  email: string;
  password: string;
  name: string;
  role?: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN';
}

export interface UpdateUserData {
  name?: string;
  role?: 'STUDENT' | 'INSTRUCTOR' | 'ADMIN';
  isOnboarded?: boolean;
  avatar?: string;
  bio?: string;
  skills?: string[];
  interests?: string[];
}

class AppwriteUserService {
  // Create a new user account
  async createUser(userData: CreateUserData): Promise<AppwriteUser> {
    try {
      const account = getAccount();
      const databases = getDatabases();
      const config = getAppwriteConfig();

      // Create account with Appwrite Auth
      const newAccount = await account.create(
        ID.unique(),
        userData.email,
        userData.password,
        userData.name
      );

      // Create user document in database
      const userDoc = await databases.createDocument(
        config.databaseId,
        config.usersCollectionId,
        newAccount.$id,
        {
          email: userData.email,
          name: userData.name,
          role: userData.role || 'STUDENT',
          isOnboarded: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );

      return userDoc as AppwriteUser;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<AppwriteUser | null> {
    try {
      const databases = getDatabases();
      const config = getAppwriteConfig();
      const user = await databases.getDocument(
        config.databaseId,
        config.usersCollectionId,
        userId
      );
      return user as AppwriteUser;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }

  // Get user by email
  async getUserByEmail(email: string): Promise<AppwriteUser | null> {
    try {
      const users = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.usersCollectionId,
        [Query.equal('email', email)]
      );

      if (users.documents.length > 0) {
        return users.documents[0] as AppwriteUser;
      }
      return null;
    } catch (error) {
      console.error('Error getting user by email:', error);
      return null;
    }
  }

  // Update user
  async updateUser(userId: string, updateData: UpdateUserData): Promise<AppwriteUser> {
    try {
      const updatedUser = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.usersCollectionId,
        userId,
        {
          ...updateData,
          updatedAt: new Date().toISOString(),
        }
      );
      return updatedUser as AppwriteUser;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Get all users (admin only)
  async getAllUsers(): Promise<AppwriteUser[]> {
    try {
      const users = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.usersCollectionId
      );
      return users.documents as AppwriteUser[];
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  // Login user
  async loginUser(email: string, password: string) {
    try {
      const session = await account.createEmailPasswordSession(email, password);
      return session;
    } catch (error) {
      console.error('Error logging in user:', error);
      throw error;
    }
  }

  // Logout user
  async logoutUser() {
    try {
      await account.deleteSession('current');
    } catch (error) {
      console.error('Error logging out user:', error);
      throw error;
    }
  }

  // Get current user session
  async getCurrentUser() {
    try {
      const user = await account.get();
      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Delete user
  async deleteUser(userId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        appwriteConfig.databaseId,
        appwriteConfig.usersCollectionId,
        userId
      );
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }
}

export const appwriteUserService = new AppwriteUserService();
export default appwriteUserService;
