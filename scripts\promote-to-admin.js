const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function promoteToAdmin() {
  try {
    // Get all users
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log('📋 All users in database:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.name}) - Role: ${user.role}`);
    });

    if (allUsers.length === 0) {
      console.log('❌ No users found. Please create an account first.');
      return;
    }

    // Promote the most recent user to admin (or you can modify this logic)
    const userToPromote = allUsers[0]; // Most recent user
    
    if (userToPromote.role === 'ADMIN') {
      console.log(`✅ User ${userToPromote.email} is already an admin.`);
    } else {
      const updatedUser = await prisma.user.update({
        where: { id: userToPromote.id },
        data: { role: 'ADMIN' }
      });
      console.log(`✅ Promoted ${updatedUser.email} to admin!`);
    }

    console.log('\n🎉 You can now access the admin panel at: http://localhost:3001/admin');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

promoteToAdmin();
